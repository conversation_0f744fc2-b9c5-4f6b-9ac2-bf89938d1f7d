// 茶水系统 Mixin
// 负责处理茶水抽取、设置和余额管理

import { RoomService } from "@/utils/room.js";
import { showToast } from "@/utils/commonMethods.js";
import { calculateTeaDeduction, updateBalance, formatAmountDisplay } from "@/utils/mathUtils.js";

export default {
  data() {
    return {
      // 茶水相关数据
      teaWaterBalance: 0.0,
      teaWaterLimitAmount: null,
      teaWaterRatio: 0,
    };
  },

  computed: {
    // 茶水分数badge的样式类
    getTeaScoreBadgeClass() {
      return this.teaWaterBalance >= 0 ? "positive" : "negative";
    },

    // 茶水分数badge的字体大小
    getTeaScoreFontSize() {
      const absScore = Math.abs(this.teaWaterBalance);
      if (absScore >= 1000000) {
        return "8px";
      } else if (absScore >= 100000) {
        return "9px";
      } else if (absScore >= 10000) {
        return "9px";
      } else {
        return "10px";
      }
    },
  },

  methods: {
    // ==================== 茶水设置 ====================
    
    /**
     * 计算茶水抽佣
     * @param {number} originalAmount 原始分数
     * @returns {Object} { teaAmount: 抽取的茶水金额, actualAmount: 玩家实际得到的分数 }
     */
    calculateTeaWaterDeduction(originalAmount) {
      console.log(this.teaWaterRatio,'this.teaWaterRatio--------')
      console.log(originalAmount,'originalAmount--------')
      
      // 使用精确计算的茶水抽佣函数
      const result = calculateTeaDeduction(
        originalAmount, 
        this.teaWaterRatio, 
        this.teaWaterBalance, 
        this.teaWaterLimitAmount
      );
      
      console.log(result.teaAmount,'teaAmount--------')
      return result;
    },

    /**
     * 显示茶水设置弹窗
     */
    showTeaSettings() {
      this.showTeaModal = true;
    },

    /**
     * 关闭茶水设置弹窗
     */
    closeTeaSettings() {
      this.showTeaModal = false;
    },

    /**
     * 保存茶水设置
     * @param {Object} teaData 茶水设置数据
     */
    async saveTeaSettings(teaData) {
      try {
        // 确保数据类型正确
        const limitAmount = teaData.limitAmount === null || teaData.limitAmount === undefined || teaData.limitAmount === '' 
          ? null 
          : Number(teaData.limitAmount);
        const ratio = Number(teaData.ratio || 0);
        
        // 验证数据有效性
        if (isNaN(ratio) || ratio < 0 || ratio > 10) {
          showToast("茶水比例必须在0-10%之间", "none");
          return;
        }
        
        if (limitAmount !== null && (isNaN(limitAmount) || limitAmount < 0)) {
          showToast("茶水上限必须为正数或留空", "none");
          return;
        }

        // 优先尝试WebSocket方式
        if (this.isWebSocketEnabled && this.wsSaveTeaSettings) {
          console.log('尝试使用WebSocket保存茶水设置');
          
          const wsResult = await this.wsSaveTeaSettings({
            teaWaterLimitAmount: limitAmount,
            teaWaterRatio: ratio
          });
          
          if (wsResult.success) {
            // WebSocket成功，显示成功提示并关闭弹窗
            showToast("茶水设置已保存");
            this.closeTeaSettings();
            
            // 如果是给分玩法，添加到流水记录中
            if (!this.isTraditionalView && this.addTransactionRecord) {
              this.addTransactionRecord(
                this.realCurrentUserId,
                "设置",
                `修改茶水设置：${ratio}%`,
                0
              );
            }
            
            // 滚动到底部（如果是传统记分视图）
            if (this.isTraditionalView && this.scrollToBottom) {
              this.scrollToBottom();
            }
            
            console.log('WebSocket茶水设置保存成功');
            return;
          } else {
            console.warn('WebSocket茶水设置保存失败，降级到云对象模式:', wsResult.message);
          }
        }
        
        // WebSocket失败或不可用，降级到原有的云对象方式
        console.log('使用云对象保存茶水设置');
        await this.saveTeaSettingsByCloudFunction({
          limitAmount: limitAmount,
          ratio: ratio,
          limitText: teaData.limitText
        });
        
      } catch (error) {
        console.error('保存茶水设置失败:', error);
        showToast("茶水设置保存失败，请重试", "none");
      }
    },

    /**
     * 通过云对象保存茶水设置（原有逻辑）
     * @param {Object} teaData 茶水设置数据
     */
    async saveTeaSettingsByCloudFunction(teaData) {
      try {
        // 确保数据类型正确
        const limitAmount = teaData.limitAmount === null || teaData.limitAmount === undefined || teaData.limitAmount === '' 
          ? null 
          : Number(teaData.limitAmount);
        const ratio = Number(teaData.ratio || 0);
        
        // 验证数据有效性
        if (isNaN(ratio) || ratio < 0 || ratio > 10) {
          throw new Error('茶水比例必须在0-10%之间');
        }
        
        if (limitAmount !== null && (isNaN(limitAmount) || limitAmount < 0)) {
          throw new Error('茶水上限必须为正数或留空');
        }

        // 更新本地状态
        this.teaWaterLimitAmount = limitAmount;
        this.teaWaterRatio = ratio;

        // 保存到数据库
        const updateResult = await RoomService.updateRoomTeaSettings(this.roomInfo.id, {
          teaWaterLimitAmount: limitAmount,
          teaWaterRatio: ratio
        });

        if (!updateResult.success) {
          throw new Error(updateResult.message || '保存茶水设置失败');
        }

        // 添加系统消息记录
        const messageResult = await this.addRoomMessage("system", {
          message_text: `修改了茶水设置，抽取比例：${ratio}%，抽取上限：${teaData.limitText}`,
          action_type: "tea_settings"
        });

        // 如果是给分玩法，也添加到流水记录中
        if (!this.isTraditionalView && this.addTransactionRecord) {
          this.addTransactionRecord(
            this.realCurrentUserId,
            "设置",
            `修改茶水设置：${ratio}%`,
            0
          );
        }

        if (messageResult.success) {
          showToast("茶水设置已保存");
        } else {
          showToast("茶水设置保存成功，但消息记录失败", "none");
        }
        
        this.closeTeaSettings();

        if (this.isTraditionalView && this.scrollToBottom) {
          this.scrollToBottom();
        }
      } catch (error) {
        console.error('云对象茶水设置保存失败:', error);
        showToast(error.message || "茶水设置保存失败，请重试", "none");
      }
    },

    // ==================== 事件处理方法 ====================
    
    /**
     * 处理房间茶水设置加载事件
     * @param {Object} teaSettings 茶水设置数据
     */
    handleTeaSettingsLoaded(teaSettings) {
      // 确保 teaWaterLimitAmount 的类型正确 - 数值类型或null
      if (teaSettings.teaWaterLimitAmount === null || teaSettings.teaWaterLimitAmount === undefined) {
        this.teaWaterLimitAmount = null;
      } else {
        // 将字符串转换为数值，如果转换失败则使用null
        const limitAmount = Number(teaSettings.teaWaterLimitAmount);
        this.teaWaterLimitAmount = isNaN(limitAmount) ? null : limitAmount;
      }
      
      // 确保 teaWaterRatio 的类型正确 - 数值类型
      const ratio = Number(teaSettings.teaWaterRatio || 0);
      this.teaWaterRatio = isNaN(ratio) ? 0 : ratio;
      
      // 确保 teaWaterBalance 的类型正确 - 数值类型
      const balance = Number(teaSettings.teaWaterBalance || 0);
      this.teaWaterBalance = isNaN(balance) ? 0 : balance;
      
      console.log('茶水设置已加载:', {
        teaWaterLimitAmount: this.teaWaterLimitAmount,
        teaWaterRatio: this.teaWaterRatio,
        teaWaterBalance: this.teaWaterBalance
      });
    },

    /**
     * 处理茶水收取事件
     * @param {number} amount 收取的茶水金额
     */
    async handleTeaAmountCollected(amount) {
      try {
        // 使用精确计算更新本地茶水余额
        this.teaWaterBalance = updateBalance(this.teaWaterBalance, amount);

        // 同步到云端
        const result = await RoomService.updateTeaWaterBalance(this.roomInfo.id, amount);
        if (!result.success) {
          console.error('同步茶水余额到云端失败:', result.message);
        }
      } catch (error) {
        console.error('处理茶水收取失败:', error);
      }
    },

    // ==================== 茶水计分功能 ====================

    /**
     * 处理茶水按钮点击事件
     */
    handleTeaClick() {
      // 检查房间人数，只有自己时显示提示
      if (this.players.length <= 1) {
        showToast("房间内还没有其他玩家，请邀请后再计分", "none");
        return;
      }

      // 打开茶水计分弹窗
      this.scoreModalType = "tea";
      this.selectedPlayer = null;
      this.showScoreModal = true;
    },

    /**
     * 处理茶水计分
     * @param {Object} param0 计分参数
     */
    async handleTeaScore({ score }) {
      try {
        // 优先尝试WebSocket方式
        if (this.isWebSocketEnabled && this.wsTeaScore) {
          console.log('尝试使用WebSocket进行茶水计分');

          const wsResult = await this.wsTeaScore(score);

          if (wsResult.success) {
            // WebSocket成功，显示成功提示
            showToast(`已给茶水记分${formatAmountDisplay(score)}分`);
            console.log('WebSocket茶水计分成功');
            return;
          } else {
            console.warn('WebSocket茶水计分失败，降级到云对象模式:', wsResult.message);
          }
        }

        // WebSocket失败或不可用，降级到原有的云对象方式
        console.log('使用云对象进行茶水计分');
        await this.handleTeaScoreByCloudFunction({ score });

      } catch (error) {
        console.error('茶水计分处理异常:', error);
        showToast("茶水计分失败，请重试", "none");
      }
    },

    /**
     * 通过云对象处理茶水计分（原有逻辑）
     * @param {Object} param0 计分参数
     */
    async handleTeaScoreByCloudFunction({ score }) {
      // 创建一个虚拟的茶水"玩家"对象用于消息记录
      const teaPlayer = {
        id: "tea_water",
        name: "茶水",
        avatarFileId: null
      };

      // 添加消息记录到数据库，茶水计分不需要抽佣，直接记录原始分数
      const messageResult = await this.addRoomMessage("score", {
        target_id: teaPlayer.id,
        target_name: teaPlayer.name,
        target_avatar_fileId: teaPlayer.avatarFileId || "",
        amount: score,
        original_amount: score,
        tea_amount: 0 // 茶水计分不抽取茶水
      });

      if (messageResult.success) {
        // 直接更新茶水余额
        this.handleTeaAmountCollected(score);

        showToast(`已给茶水记分${formatAmountDisplay(score)}分`);
      } else {
        showToast("茶水计分失败，请重试", "none");
      }
    },
  }
};