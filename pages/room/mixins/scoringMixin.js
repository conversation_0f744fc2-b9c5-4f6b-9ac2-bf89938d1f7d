// 计分系统 Mixin
// 负责处理单个玩家计分和批量计分功能

import { showToast } from "@/utils/commonMethods.js";
import { safeAdd, safeMinus, formatAmountDisplay } from "@/utils/mathUtils.js";
import { calculatePersonalSettlement, generateSettlementDetails, formatSettlementAmount } from "@/utils/settlementUtils.js";

export default {
  data() {
    return {
      // 计分弹窗状态
      showScoreModal: false,
      scoreModalType: "single",
      selectedPlayer: null,
    };
  },

  computed: {
    // 当前视图的操作按钮
    currentViewActions() {
      if (this.isTraditionalView) {
        const actions = [];

        // 只有房间未退出人数>=2时才显示生成结算按钮
        // 使用已定义的 activePlayers computed 属性
        if (this.activePlayers && this.activePlayers.length >= 2) {
          actions.push({
            name: "生成结算",
            class: "settlement",
            handler: this.generateSettlement,
          });
        }

        // 记录一笔支出按钮始终显示
        actions.push({
          name: "记录一笔支出",
          class: "record",
          handler: this.recordExpense,
        });

        return actions;
      } else {
        return [
          { name: "收分", class: "scoring", handler: this.collectScore },
          {
            name: "出分到桌面",
            class: "distribution",
            handler: this.distributeScore,
          },
        ];
      }
    },

    // 是否有未结算账单（仅传统玩法需要检查）
    hasNoSettleAccount() {
      // 给分玩法不需要此检查
      if (!this.isTraditionalView) {
        return false;
      }

      // 检查是否有任何未退出玩家的分数不为0
      if (!this.activePlayers || this.activePlayers.length === 0) {
        return false;
      }

      // 只检查未退出玩家的分数，如果有任何未退出玩家的分数不为0，说明有未结算的给分记录
      // 使用已定义的 activePlayers computed 属性
      const hasNonZeroScore = this.activePlayers.some(player => player.score !== 0);

      // 如果有茶水余额，也算作未结算
      const hasTeaBalance = this.teaWaterBalance && this.teaWaterBalance !== 0;

      return hasNonZeroScore || hasTeaBalance;
    },
  },

  methods: {
    // ==================== 计分相关方法 ====================
    
    /**
     * 为指定玩家记分
     * @param {Object} player 玩家对象
     */
    recordScoreForPlayer(player) {
      this.scoreModalType = "single";
      this.selectedPlayer = player;
      this.showScoreModal = true;
    },

    /**
     * 关闭计分弹窗
     */
    closeScoreModal() {
      Object.assign(this, {
        showScoreModal: false,
        selectedPlayer: null,
        scoreModalType: "single",
      });
    },

    /**
     * 处理计分确认
     * @param {Object} scoreData 计分数据
     */
    handleScoreConfirm(scoreData) {
      if (scoreData.type === "single") {
        this.handleSinglePlayerScore(scoreData);
      } else if (scoreData.type === "multiple") {
        this.handleMultiplePlayerScore(scoreData);
      } else if (scoreData.type === "tea") {
        this.handleTeaScore(scoreData);
      }

      this.closeScoreModal();

      // 滚动到底部（如果是传统记分视图）
      if (this.scrollToBottom) {
        this.scrollToBottom();
      }
    },

    /**
     * 处理单个玩家计分
     * @param {Object} param0 计分参数
     */
    async handleSinglePlayerScore({ player, score }) {
      try {
        // 优先尝试WebSocket方式
        if (this.isWebSocketEnabled && this.wsScoreSinglePlayer) {
          console.log('尝试使用WebSocket进行单人给分');
          
          const wsResult = await this.wsScoreSinglePlayer(player, score);
          
          if (wsResult.success) {
            // WebSocket成功，显示成功提示
            const { teaAmount = 0, actualAmount = score } = wsResult.data?.incremental?.newMessage || {};
            
            if (teaAmount > 0) {
              showToast(`已给${player.name}记分，茶水抽取${formatAmountDisplay(teaAmount)}分`);
            } else {
              showToast(`已给${player.name}记分`);
            }
            
            console.log('WebSocket单人给分成功');
            return;
          } else {
            console.warn('WebSocket单人给分失败，降级到云对象模式:', wsResult.message);
          }
        }
        
        // WebSocket失败或不可用，降级到原有的云对象方式
        console.log('使用云对象进行单人给分');
        await this.handleSinglePlayerScoreByCloudFunction({ player, score });
        
      } catch (error) {
        console.error('单人给分处理异常:', error);
        showToast("记分失败，请重试", "none");
      }
    },

    /**
     * 通过云对象处理单个玩家计分（原有逻辑）
     * @param {Object} param0 计分参数
     */
    async handleSinglePlayerScoreByCloudFunction({ player, score }) {
      // 计算茶水抽佣
      const { teaAmount, actualAmount } = this.calculateTeaWaterDeduction 
        ? this.calculateTeaWaterDeduction(score) 
        : { teaAmount: 0, actualAmount: score };
      
      // 添加消息记录到数据库，保存完整的计分信息
      const messageResult = await this.addRoomMessage("score", {
        target_id: player.id,
        target_name: player.name,
        target_avatar_fileId: player.avatarFileId || "",
        amount: actualAmount,
        original_amount: score,
        tea_amount: teaAmount
      });
      
      if (messageResult.success) {
        // 更新茶水余额（通过事件通知茶水mixin）
        if (teaAmount > 0) {
          this.handleTeaAmountCollected(teaAmount)
          
          showToast(`已给${player.name}记分，茶水抽取${formatAmountDisplay(teaAmount)}分`);
          
          // 添加茶水抽取的系统消息
          const teaMessage = await this.addRoomMessage("system", {
            message_text: `系统抽取茶水${formatAmountDisplay(teaAmount)}分，${player.name}实际得到${formatAmountDisplay(actualAmount)}分，当前茶水余额${formatAmountDisplay(this.teaWaterBalance)}分`,
            action_type: "tea_deduction"
          });
          
          if (teaMessage.success && this.isTraditionalView && this.scrollToBottom) {
            this.scrollToBottom();
          }
        } else {
          showToast(`已给${player.name}记分`);
        }
      } else {
        showToast("记分失败，请重试", "none");
      }
    },

    /**
     * 处理批量玩家计分
     * @param {Object} param0 计分参数
     */
    async handleMultiplePlayerScore({ entries }) {
      try {
        // 优先尝试WebSocket方式
        if (this.isWebSocketEnabled && this.wsScoreMultiplePlayers) {
          console.log('尝试使用WebSocket进行多人给分');
          
          const wsResult = await this.wsScoreMultiplePlayers(entries);
          
          if (wsResult.success) {
            // WebSocket成功，显示成功提示
            const newMessages = wsResult.data?.incremental?.newMessages || [];
            const totalTeaAmount = newMessages.reduce((sum, msg) => sum + (msg.tea_amount || 0), 0);
            
            if (totalTeaAmount > 0) {
              showToast(`已为${entries.length}位玩家记分，茶水抽取${formatAmountDisplay(totalTeaAmount)}分`);
            } else {
              showToast(`已为${entries.length}位玩家记分`);
            }
            
            console.log('WebSocket多人给分成功');
            return;
          } else {
            console.warn('WebSocket多人给分失败，降级到云对象模式:', wsResult.message);
          }
        }
        
        // WebSocket失败或不可用，降级到原有的云对象方式
        console.log('使用云对象进行多人给分');
        await this.handleMultiplePlayerScoreByCloudFunction({ entries });
        
      } catch (error) {
        console.error('多人给分处理异常:', error);
        showToast("记分失败，请重试", "none");
      }
    },

    /**
     * 通过云对象处理批量玩家计分（原有逻辑）
     * @param {Object} param0 计分参数
     */
    async handleMultiplePlayerScoreByCloudFunction({ entries }) {
      try {
        let totalTeaAmount = 0;
        let successCount = 0;
        
        // 为每个玩家添加消息记录
        for (const { player, score } of entries) {
          // 计算茶水抽佣
          const { teaAmount, actualAmount } = this.calculateTeaWaterDeduction 
            ? this.calculateTeaWaterDeduction(score) 
            : { teaAmount: 0, actualAmount: score };
          
          const messageResult = await this.addRoomMessage("score", {
            target_id: player.id,
            target_name: player.name,
            target_avatar_fileId: player.avatarFileId || "",
            amount: actualAmount,
            original_amount: score,
            tea_amount: teaAmount
          });
          
          if (messageResult.success) {
            totalTeaAmount = safeAdd(totalTeaAmount, teaAmount);
            successCount++;
          } else {
            console.error(`给${player.name}记分失败:`, messageResult.message);
          }
        }
        
        // 更新茶水余额（通过事件通知茶水mixin）
        if (totalTeaAmount > 0) {
          this.handleTeaAmountCollected(totalTeaAmount)
          
          showToast(`已为${successCount}位玩家记分，茶水抽取${formatAmountDisplay(totalTeaAmount)}分`);
          
          // 添加茶水抽取的系统消息
          const teaMessage = await this.addRoomMessage("system", {
            message_text: `系统从${successCount}位玩家抽取茶水共计${formatAmountDisplay(totalTeaAmount)}分，当前茶水余额${formatAmountDisplay(this.teaWaterBalance)}分`,
            action_type: "tea_deduction_multiple"
          });
          
          if (teaMessage.success && this.isTraditionalView && this.scrollToBottom) {
            this.scrollToBottom();
          }
        } else {
          showToast(`已为${successCount}位玩家记分`);
        }
      } catch (error) {
        console.error('批量记分失败:', error);
        showToast("记分失败，请重试", "none");
      }
    },

    // ==================== 游戏功能方法 ====================
    
    /**
     * 生成结算
     */
    async generateSettlement() {
      try {
        // 参数验证
        if (!this.roomInfo?.id) {
          showToast("房间信息异常，无法生成结算", "none");
          return;
        }

        // 检查未退出的玩家数量
        // 使用已定义的 activePlayers computed 属性
        if (!this.activePlayers || this.activePlayers.length < 2) {
          showToast("房间内玩家不足，无法生成结算", "none");
          return;
        }

        const currentUserId = this.realCurrentUserId;
        if (!currentUserId) {
          showToast("用户信息异常，无法生成结算", "none");
          return;
        }

        // 获取当前用户信息（必须是未退出的玩家）
        const currentUser = this.activePlayers.find(player => player.id === currentUserId);
        if (!currentUser) {
          showToast("无法找到当前用户信息", "none");
          return;
        }

        // 获取其他未退出的玩家信息
        const otherPlayers = this.activePlayers.filter(player => player.id !== currentUserId);
        
        // 计算个人结算方案
        const settlementResult = calculatePersonalSettlement(
          currentUserId,
          currentUser.name,
          currentUser.score,
          otherPlayers,
          this.teaWaterBalance || 0
        );

        if (!settlementResult.canSettle) {
          showToast(settlementResult.reason, "none");
          return;
        }

        // 检查是否启用WebSocket并且连接正常
        if (this.isWebSocketEnabled && this.wsGenerateSettlement) {
          console.log('使用WebSocket生成结算单');
          
          // 准备WebSocket结算单数据
          const settlementData = {
            details: settlementResult.details,
            totalLabel: settlementResult.totalLabel,
            totalAmount: settlementResult.totalAmount,
            scoreAdjustments: settlementResult.scoreAdjustments,
            newTeaBalance: settlementResult.newTeaBalance
          };

          try {
            // 通过WebSocket发送结算单生成请求
            const wsResult = await this.wsGenerateSettlement(settlementData);
            
            if (wsResult.success) {
              // WebSocket结算单生成成功，增量更新已在handleIncrementalUpdate中处理
              const netAmountText = formatSettlementAmount(settlementResult.totalAmount);
              showToast(`结算完成！${settlementResult.totalLabel}：${netAmountText}分`);

              // 滚动到底部显示新的结算单
              if (this.scrollToBottom) {
                this.scrollToBottom();
              }
            } else {
              throw new Error(wsResult.message || 'WebSocket结算单生成失败');
            }
            return;
          } catch (wsError) {
            console.warn('WebSocket结算单生成失败，回退到传统API:', wsError);
            showToast("WebSocket结算失败，尝试使用传统方式", "none");
            // 继续执行下面的传统API调用
          }
        }

        // 回退方案：使用传统的数据库API
        console.log('使用传统API生成结算单');
        
        // 生成结算详情数据
        const settlementDetails = generateSettlementDetails(settlementResult);

        // 保存结算单到数据库
        const messageResult = await this.addRoomMessage("settlement", settlementDetails);
        
        if (!messageResult.success) {
          showToast("结算单保存失败，请重试", "none");
          return;
        }


        // 成功反馈
        const netAmountText = formatSettlementAmount(settlementResult.totalAmount);
        showToast(`结算完成！${settlementResult.totalLabel}：${netAmountText}分`);

        // 滚动到底部显示新的结算单
        if (this.scrollToBottom) {
          this.scrollToBottom();
        }

      } catch (error) {
        console.error('生成结算失败:', error);
        showToast("生成结算失败，请稍后重试", "none");
      }
    },

    /**
     * 记录一笔支出
     */
    recordExpense() {
      // 检查房间人数，只有自己时显示提示
      if (this.players.length <= 1) {
        showToast("房间内还没有其他玩家，请邀请后再计分", "none");
        return;
      }
      
      this.scoreModalType = "multiple";
      this.selectedPlayer = null;
      this.showScoreModal = true;
    },
  }
}; 