<template>
  <div class="history-page">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-icon">
        <span class="iconfont history-icon">&#xe875;</span>
      </div>
      <div class="header-content">
        <div class="header-title">历史记录</div>
        <div class="header-subtitle">查看您的游戏战绩和记录</div>
      </div>
    </div>

    <!-- 顶部统计区域 -->
    <div class="stats-section">
      <div class="stat-item">
        <div class="stat-value">{{ stats.totalGames }}</div>
        <div class="stat-label">总场次</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item">
        <div class="stat-value">{{ stats.wins }}</div>
        <div class="stat-label">胜场</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item">
        <div class="stat-value">{{ stats.losses }}</div>
        <div class="stat-label">负场</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item">
        <div class="stat-value">{{ stats.draws }}</div>
        <div class="stat-label">平局</div>
      </div>
      <div class="stat-divider"></div>
      <div class="stat-item">
        <div class="stat-value">{{ stats.winRate }}</div>
        <div class="stat-label">胜率</div>
      </div>
    </div>

    <!-- 历史记录列表 -->
    <div class="history-section">
      <div class="section-title">
        <div class="title-left">
          <!-- <span class="iconfont list-icon">&#xe883;</span> -->
          游戏记录
        </div>
        <div class="title-right">
          <span class="tip-text">点击记录可查看详细信息</span>
        </div>
      </div>
      
      <!-- 加载中状态 -->
      <div v-if="isLoading && isFirstLoad" class="loading-state">
        <div class="loading-icon">正在加载...</div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="errorMessage" class="error-state">
        <div class="error-text">{{ errorMessage }}</div>
        <button class="retry-btn" @click="loadHistoryData">重试</button>
      </div>
      
      <!-- 历史记录滚动容器 -->
      <scroll-view
        v-else
        class="history-scroll"
        scroll-y
        :scroll-top="scrollTop"
        @scrolltolower="onScrollToLower"
        lower-threshold="100"
      >
        <div class="history-list" v-if="historyData.length > 0">
          <div 
            v-for="item in historyData" 
            :key="item.id"
            class="history-item"
            :class="item.result"
            @click="openHistoryDetail(item.roomId)"
          >
            <div class="result-badge">
              <div class="badge-content" :class="item.result">
                <!-- <span class="iconfont result-icon" v-if="item.result === 'win'">&#xe87d;</span>
                <span class="iconfont result-icon" v-else>&#xe884;</span> -->
                <span class="result-text">{{ getResultText(item.result) }}</span>
              </div>
            </div>
            
            <div class="history-info">
              <div class="room-name">{{ item.roomName }}</div>
              <div class="room-meta">
                <span class="room-date">{{ item.date }}</span>
                <span class="room-time" v-if="item.time">{{ item.time }}</span>
              </div>
            </div>
            
            <div class="amount-section">
              <div class="amount" :class="item.result">
                {{ item.result === 'win' ? '+' : '' }}{{ item.amount }}
              </div>
              <!-- <div class="currency">积分</div> -->
            </div>

            <!-- <div class="arrow-right">
              <span class="iconfont more-icon">&#xe883;</span>
            </div> -->
          </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" v-else-if="!isLoading">
          <div class="empty-icon">
            <span class="iconfont empty-icon-font">&#xe884;</span>
          </div>
          <div class="empty-text">暂无游戏记录</div>
          <div class="empty-hint">快去创建房间开始游戏吧！</div>
        </div>
        
        <!-- 加载更多状态 -->
        <div class="load-more-section" v-if="!isFirstLoad && historyData.length > 0">
          <div v-if="isLoadingMore" class="loading-tip">
            正在加载更多...
          </div>
          <div v-else-if="!pagination.hasMore" class="no-more-tip">
            没有更多数据了
          </div>
        </div>
      </scroll-view>
    </div>
  </div>
</template>

<script>
import loginFun from '@/utils/login.js';
import { handleError } from '@/utils/commonMethods.js';

export default {
  data() {
    return {
      historyData: [],
      stats: {
        totalGames: 0,
        wins: 0,
        losses: 0,
        draws: 0,
        winRate: '0%'
      },
      // 加载状态
      isLoading: false,
      errorMessage: '',
      
      // 分页相关状态
      pagination: {
        currentPage: 1,
        pageSize: 20,
        hasMore: true,
        total: 0
      },
      isLoadingMore: false, // 加载更多状态
      isFirstLoad: true,    // 首次加载标识
      
      // 滚动位置
      scrollTop: 0,
    }
  },
  
  onLoad() {
    this.loadHistoryData();
  },

  onPullDownRefresh() {
    this.resetPagination();
    this.loadHistoryData();
  },

  methods: {
    /**
     * 获取结果文本
     */
    getResultText(result) {
      switch (result) {
        case 'win':
          return '胜';
        case 'loss':
          return '负';
        case 'draw':
          return '平';
        default:
          return '未知';
      }
    },

    /**
     * 重置分页状态
     */
    resetPagination() {
      this.pagination.currentPage = 1;
      this.pagination.hasMore = true;
      this.pagination.total = 0;
      this.historyData = [];
      this.isFirstLoad = true;
    },

    /**
     * 加载历史记录数据
     * @param {boolean} isLoadMore 是否为加载更多
     */
    async loadHistoryData(isLoadMore = false) {
      try {
        // 检查登录状态
        if (!loginFun.checkLoginStatus()) {
          this.errorMessage = '请先登录';
          return;
        }

        // 设置加载状态
        if (isLoadMore) {
          this.isLoadingMore = true;
        } else {
          this.isLoading = true;
          this.errorMessage = '';
        }

        // 获取token
        const token = loginFun.storage.getToken();
        if (!token) {
          throw new Error('用户未登录');
        }

        // 调用云函数
        const userCloudObject = uniCloud.importObject('user');
        const result = await userCloudObject.getUserHistory(
          token, 
          this.pagination.currentPage, 
          this.pagination.pageSize
        );

        if (result.code !== 200) {
          throw new Error(result.message || '获取历史记录失败');
        }

        const { stats, historyList, pagination } = result.data;

        // 更新统计数据（仅首次加载或刷新时更新）
        if (!isLoadMore) {
          this.stats = stats;
        }

        // 更新历史记录列表
        if (isLoadMore) {
          // 加载更多：追加到现有列表
          this.historyData = [...this.historyData, ...historyList];
        } else {
          // 首次加载或刷新：替换列表
          this.historyData = historyList;
        }

        // 更新分页信息
        this.pagination = {
          ...this.pagination,
          ...pagination
        };

        // 结束下拉刷新
        if (!isLoadMore) {
          uni.stopPullDownRefresh();
        }

      } catch (error) {
        console.error('加载历史记录失败:', error);
        this.errorMessage = error.message || '加载失败，请重试';
        handleError(error, '加载历史记录失败');
        
        // 结束下拉刷新
        if (!isLoadMore) {
          uni.stopPullDownRefresh();
        }
      } finally {
        this.isLoading = false;
        this.isLoadingMore = false;
        this.isFirstLoad = false;
      }
    },

    /**
     * 滚动到底部时加载更多
     */
    onScrollToLower() {
      // 如果正在加载或没有更多数据，则不执行
      if (this.isLoadingMore || !this.pagination.hasMore) {
        return;
      }

      // 增加页码并加载更多
      this.pagination.currentPage++;
      this.loadHistoryData(true);
    },

    /**
     * 打开历史详情页面
     * @param {string} roomId 房间ID
     */
    openHistoryDetail(roomId) {
      console.log(roomId,'roomId')
      uni.navigateTo({
        url: `/pages/history/detail?roomId=${roomId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

.history-page {
  @include page-container;
  padding-bottom: $spacing-xl;
}

.page-header {
  @include card;
  padding: $spacing-xl $spacing-lg;
  margin-bottom: $spacing-base;
  display: flex;
  align-items: center;
  gap: $spacing-md;

  .header-icon {
    @include icon-container(50px, rgba(87, 107, 149, 0.1));
    font-size: 24px;
    color: $secondary-color;
    flex-shrink: 0;
  }

  .header-content {
    flex: 1;

    .header-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin-bottom: $spacing-xs;
    }

    .header-subtitle {
      font-size: $font-size-sm;
      color: $text-muted;
      line-height: 1.4;
    }
  }
}

.stats-section {
  @include card;
  padding: $spacing-lg 0;
  margin-bottom: $spacing-base;
  display: flex;
  align-items: center;
  justify-content: space-around;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-xs;

    .stat-value {
      font-size: $font-size-xxl;
      font-weight: $font-weight-semibold;
      color: $text-primary;
    }

    .stat-label {
      font-size: $font-size-sm;
      color: $text-muted;
    }
  }

  .stat-divider {
    @include divider;
    height: 30px;
  }
}

.history-section {
  @include card;
  padding: $spacing-lg;
  margin-bottom: $spacing-base;

  .section-title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: $spacing-lg;

    .title-left {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      font-size: $font-size-md;
      font-weight: $font-weight-semibold;
      color: $text-primary;
    }

    .title-right {
      margin-left: 10px;
      .tip-text {
        font-size: $font-size-sm;
        color: $text-muted;
      }
    }
  }

  // 加载状态样式
  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;
    text-align: center;

    .loading-icon {
      font-size: $font-size-base;
      color: $text-secondary;
    }
  }

  // 错误状态样式
  .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;
    text-align: center;

    .error-text {
      font-size: $font-size-base;
      color: $text-secondary;
      margin-bottom: $spacing-lg;
    }

    .retry-btn {
      background-color: $primary-color;
      color: white;
      border: none;
      border-radius: $border-radius-base;
      padding: $spacing-sm $spacing-lg;
      font-size: $font-size-sm;
      cursor: pointer;

      &:active {
        background-color: rgba(87, 107, 149, 0.8);
      }
    }
  }

  // 滚动容器样式
  .history-scroll {
    height: calc(100vh - 300px); // 调整合适的高度
    overflow: hidden;
  }

  .history-list {
    .history-item {
      @include list-item;
      padding: $spacing-md;
      margin-bottom: $spacing-sm;
      border-radius: $border-radius-base;
      border: 1px solid $border-light;
      background-color: $card-background;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background-color: $background-color;
      }

      .result-badge {
        margin-right: $spacing-md;

        .badge-content {
          @include icon-container(40px);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: $border-radius-round;
          
          &.win {
            background-color: rgba(250, 81, 81, 0.1);
            
            // .result-icon,
            .result-text {
              color: $positive-amount-color;
            }
          }

          &.loss {
            background-color: rgba(7, 193, 96, 0.1);

            // .result-icon,
            .result-text {
              color: $negative-amount-color;
            }
          }

          &.draw {
            background-color: rgba(128, 128, 128, 0.1);

            .result-text {
              color: #666666;
            }
          }

          // .result-icon {
          //   font-size: $font-size-xs;
          //   margin-bottom: 1px;
          // }

          .result-text {
            font-size: $font-size-xs;
            font-weight: $font-weight-semibold;
            line-height: 1;
          }
        }
      }

      .history-info {
        flex: 1;

        .room-name {
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-xs;
          line-height: 1.4;
        }

        .room-meta {
          display: flex;
          align-items: center;
          gap: $spacing-sm;

          .room-date {
            font-size: $font-size-sm;
            color: $text-muted;
          }

          .room-time {
            font-size: $font-size-sm;
            color: $text-muted;
            position: relative;

            &::before {
              content: '•';
              margin-right: $spacing-xs;
            }
          }
        }
      }

      .amount-section {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        margin-right: $spacing-sm;

        .amount {
          font-size: $font-size-md;
          font-weight: $font-weight-semibold;
          margin-bottom: 2px;

          &.win {
            color: $positive-amount-color;
          }

          &.loss {
            color: $negative-amount-color;
          }
        }

        // .currency {
        //   font-size: $font-size-xs;
        //   color: $text-muted;
        // }
      }

      // .arrow-right {
      //   color: $text-muted;
      //   font-size: $font-size-sm;
      // }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl $spacing-lg;
    text-align: center;

    .empty-icon {
      @include icon-container(80px, $background-color);
      font-size: 32px;
      color: $text-muted;
      margin-bottom: $spacing-lg;
    }

    .empty-text {
      font-size: $font-size-base;
      color: $text-secondary;
      margin-bottom: $spacing-xs;
    }

    .empty-hint {
      font-size: $font-size-sm;
      color: $text-muted;
    }
  }

  // 加载更多状态样式
  .load-more-section {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: $spacing-lg;

    .loading-tip,
    .no-more-tip {
      font-size: $font-size-sm;
      color: $text-muted;
      text-align: center;
    }

    .loading-tip {
      color: $text-secondary;
    }
  }
}
</style>