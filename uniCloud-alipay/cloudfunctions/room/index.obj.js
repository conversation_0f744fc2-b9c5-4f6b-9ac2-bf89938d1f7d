const jwt = require("jsonwebtoken");
const JWT_SECRET = "suixin-scorer-jwt-secret";
const NP = require("number-precision");

// 启用边界检查
NP.enableBoundaryChecking(true);

const {
  generateRoomId,
  generateRoomName,
  validateUserActiveRoom,
  formatRoomDuration,
  clearUserActiveRoom,
  validateAndRepairActiveRoomConsistency,
} = require("./room-utils");

/**
 * 验证JWT token
 * @param {string} token JWT token
 * @returns {Object} 验证结果 {valid: boolean, payload: Object, error: string}
 */
function verifyToken(token) {
  try {
    if (!token) {
      return { valid: false, error: "Token为空" };
    }

    const options = {
      issuer: "suixin-scorer",
    };

    const payload = jwt.verify(token, JWT_SECRET, options);

    return { valid: true, payload };
  } catch (error) {
    let errorMessage = "Token验证失败";
    if (error.name === "TokenExpiredError") {
      errorMessage = "Token已过期";
    } else if (error.name === "JsonWebTokenError") {
      errorMessage = "Token格式错误";
    } else if (error.name === "NotBeforeError") {
      errorMessage = "Token未生效";
    }

    return { valid: false, error: errorMessage };
  }
}

// 私有方法：验证token并获取用户信息
async function _verifyTokenAndGetUser(token, usersCollection) {
  const verifyResult = verifyToken(token);
  if (!verifyResult.valid) {
    return {
      success: false,
      code: 401,
      message: "Token无效，请重新登录",
    };
  }

  const userRes = await usersCollection
    .where({ wx_openid: verifyResult.payload.openId })
    .get();

  if (userRes.data.length === 0) {
    return {
      success: false,
      code: 404,
      message: "用户不存在",
    };
  }

  return {
    success: true,
    data: {
      openId: verifyResult.payload.openId,
      userInfo: userRes.data[0],
    },
  };
}

/**
 * 格式化时间戳
 * @param {Date} timestamp 时间戳
 * @returns {string} 格式化后的时间字符串
 */
function _formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
}

/**
 * 格式化消息详细数据
 * @param {string} messageType 消息类型
 * @param {Object} detailData 详细数据
 * @returns {Object} 格式化后的数据
 */
function _formatMessageDetailData(messageType, detailData) {
  switch (messageType) {
    case "score":
      return {
        target: {
          id: detailData.target_id,
          name: detailData.target_name,
          avatar_fileId: detailData.target_avatar_fileId || "",
        },
        amount: detailData.amount,
        original_amount: detailData.original_amount || detailData.amount,
        tea_amount: detailData.tea_amount || 0,
      };
    case "system":
      return {
        message: detailData.message_text,
        actionType: detailData.action_type,
      };
    case "settlement":
      return {
        details: detailData.details,
        totalLabel: detailData.total_label,
        totalAmount: detailData.total_amount,
        // 添加分数调整信息，用于分数计算
        score_adjustments: detailData.score_adjustments,
        new_tea_balance: detailData.new_tea_balance,
      };
    case "distribute":
      return {
        amount: detailData.amount,
        round_number: detailData.round_number,
        table_score_before: detailData.table_score_before,
        table_score_after: detailData.table_score_after,
      };
    case "collect":
      return {
        original_amount: detailData.original_amount,
        actual_amount: detailData.actual_amount,
        tea_amount: detailData.tea_amount || 0,
        round_number: detailData.round_number,
        table_score_before: detailData.table_score_before,
        table_score_after: detailData.table_score_after,
      };
    case "round_start":
    case "round_end":
      return {
        round_number: detailData.round_number,
        message: detailData.message_text,
        trigger_reason: detailData.trigger_reason,
        final_table_score: detailData.final_table_score,
      };
    default:
      return detailData;
  }
}
/**
 * 自动完成房间并计算玩家统计（私有方法）
 * @param {string} roomId 房间ID
 * @param {Object} db 数据库实例
 * @param {Object} roomsCollection 房间集合
 * @param {Object} messagesCollection 消息集合
 * @returns {Object} 完成结果
 */
async function autoFinishRoomWithStats(roomId, db, roomsCollection, messagesCollection) {
  try {
    if (!roomId) {
      return {
        code: 400,
        message: "房间ID不能为空",
      };
    }

    // 获取房间信息
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();

    if (roomRes.data.length === 0) {
      return {
        code: 404,
        message: "房间不存在",
      };
    }

    const room = roomRes.data[0];

    // 检查房间状态
    if (room.room_status === "finished") {
      return {
        code: 200,
        message: "房间已经结束",
        data: { alreadyFinished: true },
      };
    }

    // 检查是否满足自动结束条件（所有玩家都已退出）
    const activePlayers = room.players.filter(player => player.has_left !== true);
    if (activePlayers.length > 0) {
      return {
        code: 400,
        message: "房间仍有活跃玩家，无法自动结束",
        data: {
          activePlayerCount: activePlayers.length,
          activePlayerNames: activePlayers.map(p => p.nickname).join(', ')
        }
      };
    }

    // 计算玩家最终分数和茶水余额
    const calculationResult = await _calculatePlayerFinalScores(
      roomId,
      room.players,
      messagesCollection,
      db,
      room.tea_water_balance || 0
    );
    const playerScores = calculationResult.playerScores;
    const calculatedTeaBalance = calculationResult.calculatedTeaBalance;

    // 使用事务处理房间结束
    const transaction = await db.startTransaction();
    try {
      // 更新房间状态和茶水余额
      await transaction.collection("rooms").doc(room._id).update({
        room_status: "finished",
        end_time: new Date(),
        tea_water_balance: calculatedTeaBalance, // 更新重新计算的茶水余额
      });
      
      // 提交事务
      await transaction.commit();
    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback();
      throw transactionError;
    }

    // 批量更新玩家统计 - 确保所有参与过房间的玩家都被统计
    try {
      const userObj = uniCloud.importObject("user");

      // 获取所有参与过房间的玩家（包括已退出的）
      const allParticipants = room.players.map(player => ({
        userId: player.user_id,
        finalScore: 0 // 默认分数为0
      }));

      // 更新有实际分数的玩家分数
      playerScores.forEach(scoreData => {
        const participant = allParticipants.find(p => p.userId === scoreData.userId);
        if (participant) {
          participant.finalScore = scoreData.finalScore;
        }
      });

      console.log(`房间 ${roomId} 自动结束，准备更新 ${allParticipants.length} 个玩家的统计数据:`, allParticipants);

      const statsResult = await userObj.batchUpdateRoomPlayersStats(
        roomId,
        allParticipants
      );

      if (statsResult.code !== 200) {
        console.warn(
          `自动结束房间时批量更新玩家统计失败: ${statsResult.message}`
        );
      } else {
        console.log(`房间 ${roomId} 玩家统计更新成功:`, statsResult.data);
      }
    } catch (statsError) {
      console.error("自动结束房间时更新玩家统计异常:", statsError);
    }

    return {
      code: 200,
      message: "房间自动结束成功",
      data: {
        roomId,
        playerScores,
        endTime: new Date(),
      },
    };
  } catch (error) {
    console.error("自动完成房间并计算统计失败:", error);
    return {
      code: 500,
      message: "自动完成房间服务异常",
    };
  }
}

/**
 * 计算玩家最终分数和茶水余额（私有方法）
 * @param {string} roomId 房间ID
 * @param {Array} players 玩家列表
 * @param {Object} messagesCollection 消息集合
 * @param {number} initialTeaBalance 初始茶水余额
 * @returns {Object} { playerScores: Array, calculatedTeaBalance: number }
 */
async function _calculatePlayerFinalScores(roomId, players, messagesCollection, db, initialTeaBalance = 0) {
  try {
    const playerScores = [];
    let calculatedTeaBalance = initialTeaBalance;

    // 获取房间所有计分相关消息，包括 score（计分）、distribute（出分）、collect（收分）、settlement（结算）
    const messagesRes = await messagesCollection
      .where({
        room_id: roomId,
        message_type: db.command.in(["score", "distribute", "collect", "settlement"]),
      })
      .get();

    // 初始化玩家分数
    const scoreMap = {};
    const gameScoreMap = {}; // 用于记录游戏分数（不包括结算调整）
    players.forEach((player) => {
      scoreMap[player.user_id] = 0;
      gameScoreMap[player.user_id] = 0;
    });

    // 计算分数 - 分两个阶段：游戏分数和结算调整
    messagesRes.data.forEach((message) => {
      const { sender_id, message_type, detail_data } = message;
      
      if (message_type === "score") {
        // 处理玩家间直接计分消息
        const amount = detail_data.amount || 0;
        const originalAmount = detail_data.original_amount || amount;
        const targetId = detail_data.target_id;

        // 给分者减少原始分数（同时更新游戏分数和总分数）
        if (sender_id && scoreMap.hasOwnProperty(sender_id)) {
          scoreMap[sender_id] = NP.minus(scoreMap[sender_id], Math.abs(originalAmount));
          gameScoreMap[sender_id] = NP.minus(gameScoreMap[sender_id], Math.abs(originalAmount));
        }

        // 收分者增加实际分数
        if (targetId === 'tea_water') {
          // 特殊处理茶水计分：更新茶水余额
          console.log(`云函数处理茶水计分: ${calculatedTeaBalance} + ${amount} = ${NP.plus(calculatedTeaBalance, amount)}`);
          calculatedTeaBalance = NP.plus(calculatedTeaBalance, Math.abs(amount));
        } else if (targetId && scoreMap.hasOwnProperty(targetId)) {
          scoreMap[targetId] = NP.plus(scoreMap[targetId], Math.abs(amount));
          gameScoreMap[targetId] = NP.plus(gameScoreMap[targetId], Math.abs(amount));
        }

        // 处理茶水抽取（如果有的话）
        const teaAmount = detail_data.tea_amount || 0;
        if (teaAmount > 0) {
          console.log(`云函数处理茶水抽取: ${calculatedTeaBalance} + ${teaAmount} = ${NP.plus(calculatedTeaBalance, teaAmount)}`);
          calculatedTeaBalance = NP.plus(calculatedTeaBalance, Math.abs(teaAmount));
        }
      } else if (message_type === "distribute") {
        // 处理出分到桌面消息（给分玩法）
        const amount = detail_data.amount || 0;

        if (sender_id && scoreMap.hasOwnProperty(sender_id)) {
          scoreMap[sender_id] = NP.minus(scoreMap[sender_id], Math.abs(amount)); // 出分者减分
          gameScoreMap[sender_id] = NP.minus(gameScoreMap[sender_id], Math.abs(amount));
        }
      } else if (message_type === "collect") {
        // 处理从桌面收分消息（给分玩法）
        const actualAmount = detail_data.actual_amount || detail_data.amount || 0;

        if (sender_id && scoreMap.hasOwnProperty(sender_id)) {
          scoreMap[sender_id] = NP.plus(scoreMap[sender_id], Math.abs(actualAmount)); // 收分者增加实际分数
          gameScoreMap[sender_id] = NP.plus(gameScoreMap[sender_id], Math.abs(actualAmount));
        }
      } else if (message_type === "settlement") {
        // 处理结算消息的分数调整（只影响最终分数，不影响游戏分数）
        console.log("云函数处理结算消息的分数调整 - 注意：结算不影响胜负判定");

        const adjustments = parseSettlementAdjustments(
          message,
          players,
          {}
        );

        // 应用玩家分数调整（只更新scoreMap，不更新gameScoreMap）
        Object.keys(adjustments).forEach((playerId) => {
          if (playerId === 'tea_water') {
            // 处理茶水分数调整
            console.log(`云函数应用茶水分数调整: ${calculatedTeaBalance} + ${adjustments[playerId]} = ${NP.plus(calculatedTeaBalance, adjustments[playerId])}`);
            calculatedTeaBalance = NP.plus(calculatedTeaBalance, adjustments[playerId]);
          } else if (scoreMap.hasOwnProperty(playerId)) {
            console.log(`云函数应用玩家${playerId}分数调整: ${scoreMap[playerId]} + ${adjustments[playerId]} = ${NP.plus(scoreMap[playerId], adjustments[playerId])} (不影响胜负判定)`);
            scoreMap[playerId] = NP.plus(scoreMap[playerId], adjustments[playerId]);
          }
        });
      }
    });

    // 转换为数组格式 - 使用游戏分数来判定胜负，而不是结算后分数
    Object.keys(gameScoreMap).forEach((userId) => {
      playerScores.push({
        userId: userId,
        finalScore: gameScoreMap[userId], // 使用游戏分数（不包括结算调整）
        settlementScore: scoreMap[userId], // 保留结算后分数用于其他用途
      });
    });

    console.log(`房间 ${roomId} 分数计算完成:`, {
      gameScores: gameScoreMap,
      settlementScores: scoreMap,
      playerScores: playerScores,
      messageCount: messagesRes.data.length,
      messageTypes: messagesRes.data.map(m => m.message_type)
    });

    return {
      playerScores: playerScores,
      calculatedTeaBalance: calculatedTeaBalance
    };
  } catch (error) {
    console.error("计算玩家最终分数失败:", error);
    return {
      playerScores: [],
      calculatedTeaBalance: initialTeaBalance
    };
  }
}

/**
 * 向房间内的WebSocket连接广播消息（独立函数）
 * @param {string} roomId 房间ID
 * @param {Object} message 广播消息
 * @param {Object} db 数据库实例
 */
async function broadcastToRoomConnections(roomId, message, db) {
  try {
    // 获取WebSocket连接数据库集合
    const wsConnectionsCollection = db.collection("websocket_connections");
    // 查找房间内活跃的WebSocket连接
    const connectionsRes = await wsConnectionsCollection
      .where({
        room_id: roomId,
        status: 'active',
      })
      .get();
    if (connectionsRes.data.length === 0) {
      console.log(`房间 ${roomId} 没有活跃的WebSocket连接，跳过广播`);
      return { success: true, sentCount: 0 };
    }
    const ws = uniCloud.webSocketServer();
    let sentCount = 0;
    let failedCount = 0;

    // 向每个活跃连接发送消息
    for (const connection of connectionsRes.data) {
      try {
        await ws.send(connection.connection_id, JSON.stringify(message));
        sentCount++;
        console.log(`消息已发送到连接 ${connection.connection_id} (用户: ${connection.nickname})`);
      } catch (sendError) {
        failedCount++;
        console.error(`向连接 ${connection.connection_id} 发送消息失败:`, sendError);
        
        // 清理失败的连接
        try {
          await wsConnectionsCollection.doc(connection._id).update({
            status: 'expired',
            disconnect_time: new Date()
          });
        } catch (cleanupError) {
          console.error('清理失败连接时出错:', cleanupError);
        }
      }
    }

    console.log(`房间 ${roomId} 广播完成: 成功 ${sentCount}, 失败 ${failedCount}`);
    return { success: true, sentCount, failedCount };

  } catch (error) {
    console.error('WebSocket广播异常:', error);
    return { success: false, error: error.message };
  }
}

module.exports = {
  // 云对象初始化
  _before: function () {
    this.db = uniCloud.database();
    this.roomsCollection = this.db.collection("rooms");
    this.usersCollection = this.db.collection("users");
    this.messagesCollection = this.db.collection("room_messages");
  },

  /**
   * 创建房间
   * @param {string} token 用户token
   * @param {string} gameType 游戏类型：classic经典玩法，points给分玩法
   * @returns {Object} 创建结果
   */
  async createRoom(token, gameType) {
    try {
      if (!token || !gameType) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      if (!["classic", "points"].includes(gameType)) {
        return {
          code: 400,
          message: "无效的游戏类型",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 检查用户是否已有活跃房间
      const activeRoomCheck = await validateUserActiveRoom(
        userInfo._id,
        this.roomsCollection,
        this.db,
        this.usersCollection
      );
      if (activeRoomCheck.hasActiveRoom) {
        return {
          code: 409,
          message: "您已有进行中的房间，无法创建新房间",
          data: {
            activeRoom: activeRoomCheck.activeRoom,
          },
        };
      }

      // 生成房间ID和名称
      let roomId, roomName;
      let attempts = 0;
      const maxAttempts = 10;

      // 确保房间ID唯一性
      do {
        roomId = generateRoomId();
        roomName = generateRoomName();

        const existingRoom = await this.roomsCollection
          .where({ room_id: roomId })
          .get();

        if (existingRoom.data.length === 0) {
          break;
        }

        attempts++;
      } while (attempts < maxAttempts);

      if (attempts >= maxAttempts) {
        return {
          code: 500,
          message: "房间创建失败，请重试",
        };
      }

      // 创建房间数据
      const roomData = {
        room_id: roomId,
        room_name: roomName,
        creator_id: userInfo._id,
        game_type: gameType,
        room_status: "waiting",
        max_players: 8,
        current_players: 1, // 创建者自动加入
        players: [
          {
            user_id: userInfo._id,
            nickname: userInfo.nickname,
            avatar_fileId: userInfo.avatar_fileId,
            join_time: new Date(),
            is_creator: true,
            has_left: false,
          },
        ],
        create_time: new Date(),
        game_rounds: 0,
      };

      // 使用事务处理房间创建和用户统计更新
      let roomDocId;
      try {
        // 使用事务确保原子性
        const transaction = await this.db.startTransaction();

        try {
          // 插入房间记录
          const createResult = await transaction
            .collection("rooms")
            .add(roomData);
          roomDocId = createResult.id;

          // 更新用户的创建房间数统计和活跃房间ID
          await transaction
            .collection("users")
            .doc(userInfo._id)
            .update({
              created_rooms: this.db.command.inc(1),
              active_room_id: roomDocId,
            });

          // 提交事务
          await transaction.commit();

          return {
            code: 200,
            message: "房间创建成功",
            data: {
              room_id: roomId,
              room_name: roomName,
              game_type: gameType,
              creator_info: {
                nickname: userInfo.nickname,
                avatar_fileId: userInfo.avatar_fileId,
              },
            },
          };
        } catch (transactionError) {
          // 回滚事务
          await transaction.rollback();
          throw transactionError;
        }
      } catch (dbError) {
        console.error("房间创建事务失败:", dbError);
        throw dbError;
      }
    } catch (error) {
      console.error("创建房间失败:", error);
      return {
        code: 500,
        message: "创建房间服务异常",
      };
    }
  },

  /**
   * 获取用户活跃房间
   * @param {string} token 用户token
   * @returns {Object} 房间信息
   */
  async getUserActiveRoom(token) {
    try {
      if (!token) {
        return {
          code: 400,
          message: "Token不能为空",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 查询用户活跃房间
      const activeRoomCheck = await validateUserActiveRoom(
        userInfo._id,
        this.roomsCollection,
        this.db,
        this.usersCollection
      );
      if (!activeRoomCheck.hasActiveRoom) {
        return {
          code: 200,
          message: "无活跃房间",
          data: {
            hasActiveRoom: false,
            activeRoom: null,
          },
        };
      }

      const activeRoom = activeRoomCheck.activeRoom;
      // 只返回未退出成员
      const filteredPlayers = activeRoom.players.filter(
        (p) => p.has_left !== true
      );
      const currentPlayerCount = filteredPlayers.length;

      return {
        code: 200,
        message: "获取活跃房间成功",
        data: {
          hasActiveRoom: true,
          activeRoom: {
            room_id: activeRoom.room_id,
            room_name: activeRoom.room_name,
            game_type: activeRoom.game_type,
            room_status: activeRoom.room_status,
            current_players: currentPlayerCount,
            max_players: activeRoom.max_players,
            create_time: activeRoom.create_time,
            timeDisplay: formatRoomDuration(activeRoom.create_time),
            players: filteredPlayers,
          },
        },
      };
    } catch (error) {
      console.error("获取用户活跃房间失败:", error);
      return {
        code: 500,
        message: "获取房间信息服务异常",
      };
    }
  },

  /**
   * 加入房间
   * @param {string} token 用户token
   * @param {string} roomId 房间ID
   * @returns {Object} 加入结果
   */
  async joinRoom(token, roomId) {
    try {
      if (!token || !roomId) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 查询房间信息
      const roomRes = await this.roomsCollection
        .where({ room_id: roomId })
        .get();

      if (roomRes.data.length === 0) {
        return {
          code: 404,
          message: "房间不存在",
        };
      }

      const room = roomRes.data[0];

      // 检查房间状态
      if (room.room_status === "finished") {
        return {
          code: 400,
          message: "房间已结束，无法加入",
        };
      }

      // 检查是否已在房间内（未退出成员）
      const isAlreadyInRoom = room.players.some(
        (player) => player.user_id === userInfo._id && player.has_left !== true
      );
      if (isAlreadyInRoom) {
        return {
          code: 200,
          message: "您已在房间内",
          data: {
            room_info: room,
          },
        };
      }

      // 检查房间是否已满（只统计未退出成员）
      const currentPlayerCount = room.players.filter(
        (p) => p.has_left !== true
      ).length;
      if (currentPlayerCount >= room.max_players) {
        return {
          code: 400,
          message: "房间已满，无法加入",
        };
      }

      // 检查是否为已退出成员，若是则重置has_left: false，否则新增
      let updatedPlayers;
      const existedIndex = room.players.findIndex(
        (player) => player.user_id === userInfo._id && player.has_left === true
      );
      if (existedIndex !== -1) {
        updatedPlayers = room.players.map((player, idx) => {
          if (idx === existedIndex) {
            return { ...player, has_left: false, join_time: new Date() };
          }
          return player;
        });
      } else {
        // 加入房间 - 增强原子性操作
        const newPlayer = {
          user_id: userInfo._id,
          nickname: userInfo.nickname,
          avatar_fileId: userInfo.avatar_fileId,
          join_time: new Date(),
          is_creator: false,
          has_left: false,
        };
        updatedPlayers = [...room.players, newPlayer];
      }
      const newPlayerCount = updatedPlayers.filter(
        (p) => p.has_left !== true
      ).length;

      try {
        // 使用事务确保原子性
        const transaction = await this.db.startTransaction();

        try {
          // 原子性更新房间信息
          const updateResult = await transaction
            .collection("rooms")
            .doc(room._id)
            .update({
              players: updatedPlayers,
              current_players: newPlayerCount,
            });

          if (updateResult.updated === 0) {
            await transaction.rollback();
            return {
              code: 500,
              message: "加入房间失败，请重试",
            };
          }

          // 设置用户的活跃房间ID
          await transaction.collection("users").doc(userInfo._id).update({
            active_room_id: room._id,
          });

          // 提交事务
          await transaction.commit();

          // 添加WebSocket广播逻辑：通知房间内其他用户有新玩家加入
          try {
            const isReturning = existedIndex !== -1; // 判断是否为重新加入
            
            // 准备新玩家信息
            const newPlayerInfo = {
              id: userInfo._id,
              name: userInfo.nickname,
              avatar: userInfo.avatar_fileId,
              score: 0,
              hasLeft: false,
              isNewJoin: !isReturning
            };

            // 生成系统消息数据
            const messageData = {
              room_id: roomId,
              message_type: "system",
              sender_id: userInfo._id,
              sender_name: userInfo.nickname,
              sender_avatar_fileId: userInfo.avatar_fileId,
              timestamp: new Date(),
              detail_data: {
                message_text: `${userInfo.nickname} ${isReturning ? '重新' : ''}加入了房间`,
                action_type: "player_join"
              }
            };

            // 添加系统消息到数据库
            const addResult = await this.messagesCollection.add(messageData);

            // 获取WebSocket服务器实例并广播消息
            const ws = uniCloud.webSocketServer();
            if (ws) {
              // 广播给房间内的其他用户
              const broadcastMessage = {
                action: 'room_broadcast',
                operationType: 'player_join',
                operator: {
                  id: userInfo._id,
                  name: userInfo.nickname,
                  avatar: userInfo.avatar_fileId
                },
                newPlayer: {
                  id: userInfo._id,
                  name: userInfo.nickname,
                  avatar: userInfo.avatar_fileId,
                  isReturning: isReturning
                },
                incremental: {
                  newPlayer: newPlayerInfo,
                  newMessage: {
                    id: addResult.id,
                    type: "system",
                    timestamp: _formatTimestamp(messageData.timestamp),
                    sender: {
                      id: userInfo._id,
                      name: userInfo.nickname,
                      avatar_fileId: userInfo.avatar_fileId
                    },
                    message: messageData.detail_data.message_text,
                    actionType: messageData.detail_data.action_type,
                    isSelf: false
                  }
                },
                timestamp: Date.now()
              };

              // 向房间广播
              await broadcastToRoomConnections(roomId, broadcastMessage, this.db);
              
              console.log(`WebSocket广播完成 - 新玩家加入: ${userInfo.nickname}, 房间: ${roomId}`);
            }
          } catch (broadcastError) {
            console.warn('WebSocket广播失败，但不影响加入房间:', broadcastError);
            // 广播失败不影响加入房间的主流程
          }

          return {
            code: 200,
            message: "加入房间成功",
            data: {
              room_id: roomId,
              room_name: room.room_name,
            },
          };
        } catch (transactionError) {
          await transaction.rollback();
          throw transactionError;
        }
      } catch (updateError) {
        console.error("加入房间数据库操作失败:", updateError);
        return {
          code: 500,
          message: "加入房间失败，数据库操作异常",
        };
      }
    } catch (error) {
      console.error("加入房间失败:", error);
      return {
        code: 500,
        message: "加入房间服务异常",
      };
    }
  },

  /**
   * 退出房间
   * @param {string} token 用户token
   * @param {string} roomId 房间ID
   * @returns {Object} 退出结果
   */
  async leaveRoom(token, roomId) {
    try {
      if (!token || !roomId) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 查询房间信息
      const roomRes = await this.roomsCollection
        .where({ room_id: roomId })
        .get();

      if (roomRes.data.length === 0) {
        return {
          code: 404,
          message: "房间不存在",
        };
      }

      const room = roomRes.data[0];

      // 检查用户是否在房间内（未退出成员）
      const playerIndex = room.players.findIndex(
        (player) => player.user_id === userInfo._id && player.has_left !== true
      );
      if (playerIndex === -1) {
        return {
          code: 400,
          message: "您不在此房间内",
        };
      }

      // 逻辑标记退出
      const updatedPlayers = room.players.map((player) => {
        if (player.user_id === userInfo._id) {
          return { ...player, has_left: true };
        }
        return player;
      });
      // 只统计未退出成员
      const newPlayerCount = updatedPlayers.filter(
        (p) => p.has_left !== true
      ).length;

      try {
        // 原子性更新房间玩家信息
        const updateResult = await this.roomsCollection.doc(room._id).update({
          players: updatedPlayers,
          current_players: newPlayerCount,
        });

        if (updateResult.updated === 0) {
          return {
            code: 500,
            message: "退出房间失败，请重试",
          };
        }

        // 清空用户的活跃房间ID
        await clearUserActiveRoom(userInfo._id, this.usersCollection);

        // 检查是否需要自动结束房间（所有成员都已退出）
        if (newPlayerCount === 0) {
          try {
            // 使用新的统计集成的自动结束方法
            const autoFinishResult = await autoFinishRoomWithStats(
              roomId, 
              this.db, 
              this.roomsCollection, 
              this.messagesCollection
            );
            return {
              code: 200,
              message: "退出房间成功，房间已自动结束",
              data: {
                room_auto_finished: autoFinishResult.code === 200,
                stats_updated: autoFinishResult.data?.playerScores?.length > 0,
              },
            };
          } catch (autoFinishError) {
            console.error("自动结束房间失败:", autoFinishError);
            return {
              code: 200,
              message: "退出房间成功",
              data: {
                room_auto_finished: false,
                warning: "房间自动结束处理异常",
              },
            };
          }
        }

        return {
          code: 200,
          message: "退出房间成功",
        };
      } catch (updateError) {
        console.error("退出房间数据库操作失败:", updateError);
        return {
          code: 500,
          message: "退出房间失败，数据库操作异常",
        };
      }
    } catch (error) {
      console.error("退出房间失败:", error);
      return {
        code: 500,
        message: "退出房间服务异常",
      };
    }
  },

  /**
   * 获取房间信息
   * @param {string} token 用户token
   * @param {string} roomId 房间ID
   * @param {boolean} isPreview 是否为预览模式（用于扫码/分享链接访问）
   * @returns {Object} 房间信息
   */
  async getRoomInfo(token, roomId, isPreview = false) {
    try {
      if (!token || !roomId) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;
      // 查询房间信息
      const roomRes = await this.roomsCollection
        .where({ room_id: roomId })
        .get();
      if (roomRes.data.length === 0) {
        return {
          code: 404,
          message: "房间不存在",
        };
      }

      const room = roomRes.data[0];

      // 计算当前玩家数量（未退出成员）
      const filteredPlayers = room.players.filter((p) => p.has_left !== true);
      const currentPlayerCount = filteredPlayers.length;

      // 预览模式：为扫码/分享链接用户提供基本房间信息
      if (isPreview) {
        // 预览模式下，已结束的房间拒绝访问
        if (room.room_status === "finished") {
          return {
            code: 400,
            message: "房间已结束，无法预览",
          };
        }

        // 判断是否可以加入房间
        const canJoin =
          room.room_status !== "finished" &&
          currentPlayerCount < room.max_players;

        return {
          code: 200,
          message: "获取房间预览信息成功",
          data: {
            room_id: room.room_id,
            room_name: room.room_name,
            game_type: room.game_type,
            room_status: room.room_status,
            current_players: currentPlayerCount,
            max_players: room.max_players,
            players: filteredPlayers, // 预览模式也返回玩家信息，方便扫码用户确认房间
            create_time: room.create_time,
            timeDisplay: formatRoomDuration(room.create_time),
            canJoin: canJoin,
            isPreview: true,
          },
        };
      }

      // 完整模式：检查用户是否有权限查看房间信息（必须是房间内成员且未退出）
      const isInRoom = room.players.some(
        (player) => player.user_id === userInfo._id
      );
      if (!isInRoom) {
        return {
          code: 403,
          message: "无权限查看此房间信息",
        };
      }

      return {
        code: 200,
        message: "获取房间信息成功",
        data: {
          room_id: room.room_id,
          room_name: room.room_name,
          game_type: room.game_type,
          room_status: room.room_status,
          current_players: currentPlayerCount,
          max_players: room.max_players,
          players: filteredPlayers,
          create_time: room.create_time,
          timeDisplay: formatRoomDuration(room.create_time),
          game_rounds: room.game_rounds,
          teaWaterLimitAmount: room.tea_water_limit_amount || null,
          teaWaterRatio: room.tea_water_ratio || 0,
          teaWaterBalance: room.tea_water_balance || 0.0,
        },
      };
    } catch (error) {
      console.error("获取房间信息失败:", error);
      return {
        code: 500,
        message: "获取房间信息服务异常",
      };
    }
  },

  /**
   * 通过扫码加入房间
   * @param {string} token 用户token
   * @param {string} roomId 房间ID
   * @returns {Object} 加入结果
   */
  async joinRoomByQR(token, roomId) {
    try {
      if (!token || !roomId) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      // 验证房间ID格式（以SX开头的10位字符）
      if (!/^SX[0-9]{8}$/.test(roomId)) {
        return {
          code: 400,
          message: "无效的房间ID格式",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 检查用户是否已有活跃房间
      const activeRoomCheck = await validateUserActiveRoom(
        userInfo._id,
        this.roomsCollection,
        this.db,
        this.usersCollection
      );
      if (activeRoomCheck.hasActiveRoom) {
        return {
          code: 409,
          message: "您已在其他房间中，无法加入新房间",
          data: {
            activeRoom: activeRoomCheck.activeRoom,
          },
        };
      }

      // 查询目标房间信息
      const roomRes = await this.roomsCollection
        .where({ room_id: roomId })
        .get();

      if (roomRes.data.length === 0) {
        return {
          code: 404,
          message: "房间不存在或已关闭",
        };
      }

      const room = roomRes.data[0];

      // 检查房间状态
      if (room.room_status === "finished") {
        return {
          code: 400,
          message: "房间已结束，无法加入",
        };
      }

      // 检查用户是否已在房间内（未退出成员）
      const isAlreadyInRoom = room.players.some(
        (player) => player.user_id === userInfo._id && player.has_left !== true
      );
      if (isAlreadyInRoom) {
        return {
          code: 200,
          message: "您已在房间中",
          data: {
            room_id: room.room_id,
            room_name: room.room_name,
            game_type: room.game_type,
            already_in_room: true,
          },
        };
      }

      // 检查房间是否已满（只统计未退出成员）
      const currentPlayerCount = room.players.filter(
        (p) => p.has_left !== true
      ).length;
      if (currentPlayerCount >= room.max_players) {
        return {
          code: 400,
          message: "房间已满员，无法加入",
        };
      }

      // 检查是否为已退出成员，若是则重置has_left: false，否则新增
      let updatedPlayers;
      const existedIndex = room.players.findIndex(
        (player) => player.user_id === userInfo._id && player.has_left === true
      );
      if (existedIndex !== -1) {
        updatedPlayers = room.players.map((player, idx) => {
          if (idx === existedIndex) {
            return { ...player, has_left: false, join_time: new Date() };
          }
          return player;
        });
      } else {
        // 构建新玩家信息
        const newPlayer = {
          user_id: userInfo._id,
          nickname: userInfo.nickname,
          avatar_fileId: userInfo.avatar_fileId,
          join_time: new Date(),
          is_creator: false,
          has_left: false,
        };
        updatedPlayers = [...room.players, newPlayer];
      }
      const newPlayerCount = updatedPlayers.filter(
        (p) => p.has_left !== true
      ).length;

      try {
        // 原子性更新房间信息
        const updateResult = await this.roomsCollection.doc(room._id).update({
          players: updatedPlayers,
          current_players: newPlayerCount,
        });

        if (updateResult.updated === 0) {
          return {
            code: 500,
            message: "加入房间失败，请重试",
          };
        }

        // 设置用户的活跃房间ID
        await this.usersCollection.doc(userInfo._id).update({
          active_room_id: room._id,
        });

        // 添加WebSocket广播逻辑：通知房间内其他用户有新玩家加入
        try {
          const isReturning = existedIndex !== -1; // 判断是否为重新加入
          
          // 准备新玩家信息
          const newPlayerInfo = {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar: userInfo.avatar_fileId,
            score: 0,
            hasLeft: false,
            isNewJoin: !isReturning
          };

          // 生成系统消息数据
          const messageData = {
            room_id: roomId,
            message_type: "system",
            sender_id: userInfo._id,
            sender_name: userInfo.nickname,
            sender_avatar_fileId: userInfo.avatar_fileId,
            timestamp: new Date(),
            detail_data: {
              message_text: `${userInfo.nickname} ${isReturning ? '重新' : ''}加入了房间`,
              action_type: "player_join"
            }
          };

          // 添加系统消息到数据库
          const addResult = await this.messagesCollection.add(messageData);

          // 获取WebSocket服务器实例并广播消息
          const ws = uniCloud.webSocketServer();
          if (ws) {
            // 广播给房间内的其他用户
            const broadcastMessage = {
              action: 'room_broadcast',
              operationType: 'player_join',
              operator: {
                id: userInfo._id,
                name: userInfo.nickname,
                avatar: userInfo.avatar_fileId
              },
              newPlayer: {
                id: userInfo._id,
                name: userInfo.nickname,
                avatar: userInfo.avatar_fileId,
                isReturning: isReturning
              },
              incremental: {
                newPlayer: newPlayerInfo,
                newMessage: {
                  id: addResult.id,
                  type: "system",
                  timestamp: _formatTimestamp(messageData.timestamp),
                  sender: {
                    id: userInfo._id,
                    name: userInfo.nickname,
                    avatar_fileId: userInfo.avatar_fileId
                  },
                  message: messageData.detail_data.message_text,
                  actionType: messageData.detail_data.action_type,
                  isSelf: false
                }
              },
              timestamp: Date.now()
            };

            // 向房间广播
            await broadcastToRoomConnections(roomId, broadcastMessage, this.db);
            
            console.log(`WebSocket广播完成 - 新玩家扫码加入: ${userInfo.nickname}, 房间: ${roomId}`);
          }
        } catch (broadcastError) {
          console.warn('WebSocket广播失败，但不影响加入房间:', broadcastError);
          // 广播失败不影响加入房间的主流程
        }

        return {
          code: 200,
          message: "扫码加入房间成功",
          data: {
            room_id: room.room_id,
            room_name: room.room_name,
            game_type: room.game_type,
            current_players: newPlayerCount,
            max_players: room.max_players,
            timeDisplay: formatRoomDuration(room.create_time),
          },
        };
      } catch (updateError) {
        console.error("加入房间数据库操作失败:", updateError);
        return {
          code: 500,
          message: "加入房间失败，数据库操作异常",
        };
      }
    } catch (error) {
      console.error("扫码加入房间失败:", error);
      return {
        code: 500,
        message: "扫码加入房间服务异常",
      };
    }
  },

  /**
   * 验证并修复用户活跃房间数据一致性
   * @param {string} token 用户token
   * @returns {Object} 验证结果
   */
  async validateAndRepairUserActiveRoom(token) {
    try {
      if (!token) {
        return {
          code: 400,
          message: "Token不能为空",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 使用工具函数验证并修复数据一致性
      const repairResult = await validateAndRepairActiveRoomConsistency(
        userInfo._id,
        this.usersCollection,
        this.roomsCollection
      );

      return {
        code: 200,
        message: "数据一致性验证完成",
        data: {
          success: repairResult.success,
          message: repairResult.message,
          repaired: repairResult.repaired || false,
        },
      };
    } catch (error) {
      console.error("验证用户活跃房间数据一致性失败:", error);
      return {
        code: 500,
        message: "验证服务异常",
      };
    }
  },

  /**
   * 批量修复所有用户的活跃房间数据一致性（管理员功能）
   * @param {string} token 管理员token
   * @returns {Object} 修复结果
   */
  async batchRepairActiveRoomConsistency(token) {
    try {
      if (!token) {
        return {
          code: 400,
          message: "Token不能为空",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      // 获取所有有活跃房间的用户
      const usersWithActiveRoom = await this.usersCollection
        .where({
          active_room_id: this.db.command.neq(null),
        })
        .get();

      const repairResults = [];
      let successCount = 0;
      let failureCount = 0;

      for (const user of usersWithActiveRoom.data) {
        try {
          const repairResult = await validateAndRepairActiveRoomConsistency(
            user._id,
            this.usersCollection,
            this.roomsCollection
          );

          repairResults.push({
            userId: user._id,
            result: repairResult,
          });

          if (repairResult.success) {
            successCount++;
          } else {
            failureCount++;
          }
        } catch (error) {
          console.error(`修复用户${user._id}数据一致性失败:`, error);
          repairResults.push({
            userId: user._id,
            result: { success: false, message: error.message },
          });
          failureCount++;
        }
      }

      return {
        code: 200,
        message: "批量修复完成",
        data: {
          totalUsers: usersWithActiveRoom.data.length,
          successCount,
          failureCount,
          results: repairResults,
        },
      };
    } catch (error) {
      console.error("批量修复活跃房间数据一致性失败:", error);
      return {
        code: 500,
        message: "批量修复服务异常",
      };
    }
  },

  /**
   * 数据一致性健康检查（系统定时调用）
   * @param {string} token 系统token（可选，用于安全验证）
   * @returns {Object} 检查结果
   */
  async healthCheckActiveRoomConsistency(token = null) {
    try {
      console.log("[HEALTH_CHECK] 开始数据一致性健康检查");
      const checkStartTime = Date.now();

      // 获取所有有活跃房间的用户
      const usersWithActiveRoom = await this.usersCollection
        .where({
          active_room_id: this.db.command.neq(null),
        })
        .get();

      const issues = [];
      let checkedUsers = 0;
      let inconsistentUsers = 0;

      for (const user of usersWithActiveRoom.data) {
        checkedUsers++;

        try {
          // 检查用户的活跃房间是否存在
          const roomRes = await this.roomsCollection
            .doc(user.active_room_id)
            .get();

          if (roomRes.data.length === 0) {
            issues.push({
              type: "ROOM_NOT_EXISTS",
              userId: user._id,
              activeRoomId: user.active_room_id,
              severity: "HIGH",
            });
            inconsistentUsers++;
            continue;
          }

          const room = roomRes.data[0];

          // 检查房间状态
          if (room.room_status === "finished") {
            issues.push({
              type: "ROOM_FINISHED",
              userId: user._id,
              activeRoomId: user.active_room_id,
              roomStatus: room.room_status,
              severity: "MEDIUM",
            });
            inconsistentUsers++;
            continue;
          }

          // 检查用户是否在房间玩家列表中
          const userInRoom = room.players.some(
            (player) => player.user_id === user._id && player.has_left !== true
          );
          if (!userInRoom) {
            issues.push({
              type: "USER_NOT_IN_ROOM",
              userId: user._id,
              activeRoomId: user.active_room_id,
              severity: "HIGH",
            });
            inconsistentUsers++;
          }
        } catch (error) {
          issues.push({
            type: "CHECK_ERROR",
            userId: user._id,
            activeRoomId: user.active_room_id,
            error: error.message,
            severity: "CRITICAL",
          });
          inconsistentUsers++;
        }
      }

      const checkDuration = Date.now() - checkStartTime;
      const consistencyRate =
        checkedUsers > 0
          ? (((checkedUsers - inconsistentUsers) / checkedUsers) * 100).toFixed(
              2
            )
          : 100;

      // 生成健康检查报告
      const healthReport = {
        checkTime: new Date().toISOString(),
        duration: checkDuration,
        totalUsers: checkedUsers,
        inconsistentUsers: inconsistentUsers,
        consistencyRate: parseFloat(consistencyRate),
        issuesCount: issues.length,
        issues: issues,
      };

      // 记录健康检查日志
      console.log(
        `[HEALTH_CHECK] 检查完成 - 总用户: ${checkedUsers}, 不一致: ${inconsistentUsers}, 一致性率: ${consistencyRate}%, 耗时: ${checkDuration}ms`
      );

      // 高严重性问题告警
      const criticalIssues = issues.filter(
        (issue) => issue.severity === "CRITICAL" || issue.severity === "HIGH"
      );
      if (criticalIssues.length > 0) {
        console.error(
          `[HEALTH_CHECK] 发现${criticalIssues.length}个高严重性数据一致性问题:`,
          criticalIssues
        );
        // 这里可以接入告警系统，如发送邮件、短信等
      }

      return {
        code: 200,
        message: "数据一致性健康检查完成",
        data: healthReport,
      };
    } catch (error) {
      console.error("[HEALTH_CHECK] 数据一致性健康检查失败:", error);
      return {
        code: 500,
        message: "健康检查服务异常",
        error: error.message,
      };
    }
  },

  /**
   * 添加房间消息
   * @param {string} token 用户token
   * @param {string} roomId 房间ID
   * @param {string} messageType 消息类型：score/system/settlement
   * @param {Object} detailData 消息详细数据
   * @returns {Object} 添加结果
   */
  async addRoomMessage(token, roomId, messageType, detailData) {
    try {
      if (!token || !roomId || !messageType || !detailData) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      if (
        ![
          "score",
          "system",
          "settlement",
          "distribute",
          "collect",
          "round_start",
          "round_end",
        ].includes(messageType)
      ) {
        return {
          code: 400,
          message: "无效的消息类型",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 验证房间存在且用户有权限
      const roomRes = await this.roomsCollection
        .where({ room_id: roomId })
        .get();

      if (roomRes.data.length === 0) {
        return {
          code: 404,
          message: "房间不存在",
        };
      }

      const room = roomRes.data[0];

      // 检查用户是否在房间中
      const userInRoom = room.players.some(
        (player) => player.user_id === userInfo._id && player.has_left !== true
      );
      if (!userInRoom) {
        return {
          code: 403,
          message: "无权限访问该房间",
        };
      }

      // 为score消息类型补全target_avatar_fileId
      if (messageType === "score" && detailData.target_id && !detailData.target_avatar_fileId) {
        const targetPlayer = room.players.find(player => player.user_id === detailData.target_id);
        console.log('查找目标玩家:', detailData.target_id, '找到:', targetPlayer);
        if (targetPlayer) {
          detailData.target_avatar_fileId = targetPlayer.avatar_fileId || "";
          console.log('设置target_avatar_fileId:', detailData.target_avatar_fileId);
        }
      }

      // 构建消息数据
      const messageData = {
        room_id: roomId,
        message_type: messageType,
        sender_id: userInfo._id,
        sender_name: userInfo.nickname,
        sender_avatar_fileId: userInfo.avatar_fileId,
        timestamp: new Date(),
        detail_data: detailData,
      };

      // 检查是否为计分相关消息，需要标记用户参与房间
      let isTransactionMessage = false;
      let shouldMarkParticipation = false;

      if (["score", "distribute", "collect"].includes(messageType)) {
        isTransactionMessage = true;
        shouldMarkParticipation = true;
      }

      // 使用事务处理消息添加、房间状态更新和参与记录
      try {
        const transaction = await this.db.startTransaction();

        try {
          // 添加消息记录
          const addResult = await transaction
            .collection("room_messages")
            .add(messageData);

          // 检查是否需要更新房间状态
          if (messageType === "score" && room.room_status === "waiting") {
            const activePlayers = room.players.filter(player => player.has_left !== true);
            if (activePlayers.length >= 2) {
              await transaction.collection("rooms").doc(room._id).update({
                room_status: "playing",
                start_time: new Date(),
              });
            }
          }

          // 如果是计分相关消息，标记用户参与房间
          if (shouldMarkParticipation) {
            try {
              // 调用用户云对象标记参与
              const userObj = uniCloud.importObject("user");
              
              // 标记发送者参与房间
              const markResult = await userObj.markUserRoomParticipation(
                userInfo._id,
                roomId
              );

              if (markResult.code !== 200) {
                console.warn(`标记用户参与房间失败: ${markResult.message}`);
                // 不阻断主流程，只记录警告
              }

              // 对于score类型消息，还需要标记接收者（target_id）参与房间
              if (messageType === "score" && detailData.target_id && detailData.target_id !== userInfo._id) {
                try {
                  const targetMarkResult = await userObj.markUserRoomParticipation(
                    detailData.target_id,
                    roomId
                  );

                  if (targetMarkResult.code !== 200) {
                    console.warn(`标记目标用户参与房间失败: ${targetMarkResult.message}`);
                    // 不阻断主流程，只记录警告
                  }
                } catch (targetMarkError) {
                  console.error("标记目标用户参与房间异常:", targetMarkError);
                  // 不阻断主流程，只记录错误
                }
              }
            } catch (markError) {
              console.error("调用用户云对象标记参与失败:", markError);
              // 不阻断主流程，只记录错误
            }
          }

          // 提交事务
          await transaction.commit();

          return {
            code: 200,
            message: "消息添加成功",
            data: {
              messageId: addResult.id,
              roomStatus:
                room.current_players >= 2 && messageType === "score"
                  ? "playing"
                  : room.room_status,
              isTransactionMessage: isTransactionMessage,
            },
          };
        } catch (transactionError) {
          await transaction.rollback();
          throw transactionError;
        }
      } catch (dbError) {
        console.error("消息添加事务失败:", dbError);
        throw dbError;
      }
    } catch (error) {
      console.error("添加房间消息失败:", error);
      return {
        code: 500,
        message: "添加消息服务异常",
      };
    }
  },

  /**
   * 获取房间消息列表
   * @param {string} token 用户token
   * @param {string} roomId 房间ID
   * @returns {Object} 消息列表
   */
  async getRoomMessages(token, roomId) {
    try {
      if (!token || !roomId) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 验证房间存在且用户有权限
      const roomRes = await this.roomsCollection
        .where({ room_id: roomId })
        .get();

      if (roomRes.data.length === 0) {
        return {
          code: 404,
          message: "房间不存在",
        };
      }

      const room = roomRes.data[0];

      // 检查用户是否在房间中
      const userInRoom = room.players.some(
        (player) => player.user_id === userInfo._id
      );
      if (!userInRoom) {
        return {
          code: 403,
          message: "无权限访问该房间",
        };
      }

      // 构建查询条件 - 获取所有消息
      const messagesRes = await this.messagesCollection
        .where({ room_id: roomId })
        .orderBy("timestamp", "asc")
        .get();

      // 转换数据格式
      const messages = messagesRes.data.map((msg) => {
        const baseMessage = {
          id: msg._id,
          type: msg.message_type,
          timestamp: _formatTimestamp(msg.timestamp),
          sender: {
            id: msg.sender_id,
            name: msg.sender_name,
            avatar_fileId: msg.sender_avatar_fileId,
          },
          isSelf: msg.sender_id === userInfo._id,
          ..._formatMessageDetailData(msg.message_type, msg.detail_data),
        };

        // 为settlement类型消息添加creator字段以兼容前端组件
        if (msg.message_type === "settlement") {
          baseMessage.creator = {
            name: msg.sender_name,
            isSelf: msg.sender_id === userInfo._id,
          };
        }

        // 为给分玩法相关消息提升关键字段到顶层，确保前端能正确获取
        if (msg.detail_data && (msg.message_type === "distribute" || msg.message_type === "collect" || msg.message_type === "round_start" || msg.message_type === "round_end")) {
          if (msg.detail_data.round_number !== undefined) {
            baseMessage.round_number = msg.detail_data.round_number;
          }
        }

        return baseMessage;
      });

      return {
        code: 200,
        message: "获取消息成功",
        data: {
          messages: messages,
          total: messages.length,
        },
      };
    } catch (error) {
      console.error("获取房间消息失败:", error);
      return {
        code: 500,
        message: "获取消息服务异常",
      };
    }
  },

  /**
   * 更新房间茶水余额
   * @param {string} token 用户token
   * @param {string} roomId 房间ID
   * @param {number} teaAmountChange 茶水余额变化量
   * @returns {Object} 更新结果
   */
  async updateTeaWaterBalance(token, roomId, teaAmountChange) {
    try {
      if (!token || !roomId || typeof teaAmountChange !== "number") {
        return {
          code: 400,
          message: "参数不完整或类型错误",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 验证房间存在
      const roomRes = await this.roomsCollection
        .where({ room_id: roomId })
        .get();

      if (roomRes.data.length === 0) {
        return {
          code: 404,
          message: "房间不存在",
        };
      }

      const room = roomRes.data[0];

      // 检查用户是否在房间中
      const userInRoom = room.players.some(
        (player) => player.user_id === userInfo._id && player.has_left !== true
      );
      if (!userInRoom) {
        return {
          code: 403,
          message: "无权限操作该房间",
        };
      }

      // 计算新的茶水余额
      const currentBalance = room.tea_water_balance || 0;
      const newBalance = Math.max(0, NP.plus(currentBalance, teaAmountChange));

      // 更新茶水余额
      const updateResult = await this.roomsCollection.doc(room._id).update({
        tea_water_balance: newBalance,
      });

      if (updateResult.updated === 0) {
        return {
          code: 500,
          message: "更新茶水余额失败",
        };
      }

      return {
        code: 200,
        message: "茶水余额更新成功",
        data: {
          previousBalance: currentBalance,
          newBalance: newBalance,
          change: teaAmountChange,
        },
      };
    } catch (error) {
      console.error("更新茶水余额失败:", error);
      return {
        code: 500,
        message: "更新茶水余额服务异常",
      };
    }
  },

  /**
   * 更新房间茶水设置
   * @param {string} token 用户token
   * @param {string} roomId 房间ID
   * @param {Object} teaSettings 茶水设置 { teaWaterLimitAmount, teaWaterRatio }
   * @returns {Object} 更新结果
   */
  async updateRoomTeaSettings(token, roomId, teaSettings) {
    try {
      if (!token || !roomId || !teaSettings) {
        return {
          code: 400,
          message: "参数不完整",
        };
      }

      // 验证用户token
      const userVerifyResult = await _verifyTokenAndGetUser(
        token,
        this.usersCollection
      );
      if (!userVerifyResult.success) {
        return {
          code: userVerifyResult.code,
          message: userVerifyResult.message,
        };
      }

      const { userInfo } = userVerifyResult.data;

      // 验证房间存在
      const roomRes = await this.roomsCollection
        .where({ room_id: roomId })
        .get();

      if (roomRes.data.length === 0) {
        return {
          code: 404,
          message: "房间不存在",
        };
      }

      const room = roomRes.data[0];

      // 检查用户是否在房间中
      const userInRoom = room.players.some(
        (player) => player.user_id === userInfo._id && player.has_left !== true
      );
      if (!userInRoom) {
        return {
          code: 403,
          message: "无权限修改该房间设置",
        };
      }

      // 验证茶水设置参数
      const { teaWaterLimitAmount, teaWaterRatio } = teaSettings;

      if (
        teaWaterRatio !== undefined &&
        (teaWaterRatio < 0 || teaWaterRatio > 10)
      ) {
        return {
          code: 400,
          message: "茶水比例应在0-10%之间",
        };
      }

      if (
        teaWaterLimitAmount !== undefined &&
        teaWaterLimitAmount !== null &&
        teaWaterLimitAmount < 0
      ) {
        return {
          code: 400,
          message: "茶水上限不能为负数",
        };
      }

      // 构建更新数据
      const updateData = {};
      if (teaWaterLimitAmount !== undefined) {
        updateData.tea_water_limit_amount = teaWaterLimitAmount;
      }
      if (teaWaterRatio !== undefined) {
        updateData.tea_water_ratio = teaWaterRatio;
      }

      // 更新房间茶水设置
      const updateResult = await this.roomsCollection
        .doc(room._id)
        .update(updateData);

      if (updateResult.updated === 0) {
        return {
          code: 500,
          message: "更新茶水设置失败",
        };
      }

      return {
        code: 200,
        message: "茶水设置更新成功",
        data: {
          teaWaterLimitAmount: teaWaterLimitAmount,
          teaWaterRatio: teaWaterRatio,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      console.error("更新茶水设置失败:", error);
      return {
        code: 500,
        message: "更新茶水设置服务异常",
      };
    }
  },

  /**
   * 检查并自动结束空房间
   * @param {string} roomId 房间ID
   * @returns {Object} 操作结果
   */
  async autoFinishRoomIfEmpty(roomId) {
    try {
      if (!roomId) {
        return {
          code: 400,
          message: "房间ID不能为空",
        };
      }

      console.log(`检查房间是否为空并自动结束: ${roomId}`);

      // 调用内部的自动结束方法
      const result = await autoFinishRoomWithStats(
        roomId,
        this.db,
        this.roomsCollection,
        this.messagesCollection
      );

      // 记录操作结果
      if (result.code === 200) {
        if (result.data && result.data.alreadyFinished) {
          console.log(`房间 ${roomId} 已经是结束状态`);
        } else {
          console.log(`房间 ${roomId} 自动结束成功`);
        }
      } else if (result.code === 400 && result.message.includes("活跃玩家")) {
        console.log(`房间 ${roomId} 仍有活跃玩家，跳过自动结束`);
      } else {
        console.warn(`房间 ${roomId} 自动结束失败: ${result.message}`);
      }

      return result;
    } catch (error) {
      console.error("自动结束空房间异常:", error);
      return {
        code: 500,
        message: "自动结束房间服务异常",
      };
    }
  },

};
