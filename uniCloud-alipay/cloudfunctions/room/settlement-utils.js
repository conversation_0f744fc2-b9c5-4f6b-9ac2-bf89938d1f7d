/**
 * 结算工具模块 - 云函数版本
 * 负责处理结算相关的工具函数
 */
/**
 * 从结算详情中解析玩家分数调整信息
 * @param {Object} settlementMessage 结算消息对象
 * @param {Array} players 玩家列表 [{user_id, name}]
 * @param {Object} context 上下文信息，包含茶水余额等
 * @returns {Object} 分数调整映射 { playerId: adjustment, tea_water: teaAdjustment }
 */
function parseSettlementAdjustments(settlementMessage, players, context = {}) {
  const adjustments = {}

  // 尝试从不同的路径获取分数调整信息
  let scoreAdjustments = null;

  if (settlementMessage.detail_data && settlementMessage.detail_data.score_adjustments && Array.isArray(settlementMessage.detail_data.score_adjustments)) {
    scoreAdjustments = settlementMessage.detail_data.score_adjustments;
    console.log('云函数从 detail_data.score_adjustments 解析结算分数调整信息');
  } else if (settlementMessage.score_adjustments && Array.isArray(settlementMessage.score_adjustments)) {
    scoreAdjustments = settlementMessage.score_adjustments;
    console.log('云函数从 score_adjustments 解析结算分数调整信息');
  }

  if (scoreAdjustments) {
    scoreAdjustments.forEach(adjustment => {
      if (adjustment.playerId && typeof adjustment.adjustment === 'number') {
        adjustments[adjustment.playerId] = adjustment.adjustment;
        if (adjustment.playerId === 'tea_water') {
          console.log(`茶水分数调整: ${adjustment.adjustment} (${adjustment.reason})`);
        } else {
          console.log(`玩家${adjustment.playerId}分数调整: ${adjustment.adjustment} (${adjustment.reason})`);
        }
      }
    });
  } else {
    console.warn('结算消息中缺少分数调整信息', {
      hasDetailData: !!settlementMessage.detail_data,
      hasScoreAdjustments: !!settlementMessage.score_adjustments,
      detailDataKeys: settlementMessage.detail_data ? Object.keys(settlementMessage.detail_data) : [],
      messageKeys: Object.keys(settlementMessage)
    });
  }

  return adjustments;
}

module.exports = {
  parseSettlementAdjustments
};
