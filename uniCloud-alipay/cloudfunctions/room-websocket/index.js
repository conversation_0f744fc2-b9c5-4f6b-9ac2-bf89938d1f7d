const jwt = require("jsonwebtoken");
const Logger = require('./logger');
const JWT_SECRET = "suixin-scorer-jwt-secret";
const NP = require("number-precision");

// 启用边界检查
NP.enableBoundaryChecking(true);

// ==================== 连接管理模块 ====================

/**
 * 房间连接池 - 内存中维护房间与连接的映射关系
 * 结构: Map<roomId, Set<ConnectionInfo>>
 * ConnectionInfo: { connectionId, userId, nickname, avatarFileId, joinTime }
 */
const roomConnectionPools = new Map();

/**
 * 连接信息类
 */
class ConnectionInfo {
  constructor(connectionId, userId, nickname, avatarFileId) {
    this.connectionId = connectionId;
    this.userId = userId;
    this.nickname = nickname;
    this.avatarFileId = avatarFileId;
    this.joinTime = new Date();
    this.lastHeartbeat = new Date();
  }

  /**
   * 更新心跳时间
   */
  updateHeartbeat() {
    this.lastHeartbeat = new Date();
  }

  /**
   * 检查连接是否过期（30分钟无活动）
   */
  isExpired() {
    const now = new Date();
    const expireTime = 30 * 60 * 1000; // 30分钟
    return (now - this.lastHeartbeat) > expireTime;
  }
}

/**
 * 将连接加入房间连接池
 * @param {string} roomId 房间ID
 * @param {string} connectionId 连接ID
 * @param {string} userId 用户ID
 * @param {string} nickname 用户昵称
 * @param {string} avatarFileId 用户头像文件ID
 */
function addConnectionToRoom(roomId, connectionId, userId, nickname, avatarFileId) {
  const startTime = Date.now();
  
  Logger.debug('开始添加连接到房间', {
    roomId,
    connectionId,
    userId,
    nickname
  }, 'CONNECTION_POOL');

  if (!roomConnectionPools.has(roomId)) {
    roomConnectionPools.set(roomId, new Set());
    Logger.debug('创建新房间连接池', { roomId }, 'CONNECTION_POOL');
  }

  const roomConnections = roomConnectionPools.get(roomId);
  
  // 检查房间人数限制（最大8人）
  if (roomConnections.size >= 8) {
    Logger.warn('房间已达到最大连接数限制', {
      roomId,
      currentConnections: roomConnections.size,
      maxConnections: 8,
      connectionId,
      userId,
      nickname
    }, 'CONNECTION_POOL');
    return false;
  }

  // 移除该用户的旧连接（如果存在）
  let removedOldConnection = false;
  for (const conn of roomConnections) {
    if (conn.userId === userId) {
      roomConnections.delete(conn);
      removedOldConnection = true;
      Logger.info('移除用户旧连接', {
        userId,
        oldConnectionId: conn.connectionId,
        newConnectionId: connectionId,
        roomId
      }, 'CONNECTION_POOL');
      break;
    }
  }

  // 添加新连接
  const connectionInfo = new ConnectionInfo(connectionId, userId, nickname, avatarFileId);
  roomConnections.add(connectionInfo);
  
  const duration = Date.now() - startTime;
  Logger.info('连接已加入房间', {
    connectionId,
    userId,
    nickname,
    roomId,
    currentConnections: roomConnections.size,
    hadOldConnection: removedOldConnection,
    duration
  }, 'CONNECTION_POOL');
  
  Logger.performance('addConnectionToRoom', duration, {
    roomId,
    roomSize: roomConnections.size,
    replacedExisting: removedOldConnection
  });
  
  return true;
}

/**
 * 从房间连接池移除连接
 * @param {string} roomId 房间ID
 * @param {string} connectionId 连接ID
 */
function removeConnectionFromRoom(roomId, connectionId) {
  const startTime = Date.now();
  
  Logger.debug('开始从房间移除连接', {
    roomId,
    connectionId
  }, 'CONNECTION_POOL');

  if (!roomConnectionPools.has(roomId)) {
    Logger.warn('尝试从不存在的房间移除连接', {
      roomId,
      connectionId
    }, 'CONNECTION_POOL');
    return null;
  }

  const roomConnections = roomConnectionPools.get(roomId);
  let removedConnection = null;

  for (const conn of roomConnections) {
    if (conn.connectionId === connectionId) {
      roomConnections.delete(conn);
      removedConnection = conn;
      
      const duration = Date.now() - startTime;
      Logger.info('连接已从房间移除', {
        connectionId,
        userId: conn.userId,
        nickname: conn.nickname,
        roomId,
        remainingConnections: roomConnections.size,
        connectionDuration: Date.now() - conn.joinTime.getTime(),
        duration
      }, 'CONNECTION_POOL');
      break;
    }
  }

  if (!removedConnection) {
    Logger.warn('未找到要移除的连接', {
      roomId,
      connectionId,
      totalConnectionsInRoom: roomConnections.size
    }, 'CONNECTION_POOL');
  }

  // 如果房间没有连接了，清理房间
  if (roomConnections.size === 0) {
    roomConnectionPools.delete(roomId);
    Logger.info('房间已清空，从连接池中移除', {
      roomId
    }, 'CONNECTION_POOL');
  }

  if (removedConnection) {
    const totalDuration = Date.now() - startTime;
    Logger.performance('removeConnectionFromRoom', totalDuration, {
      roomId,
      connectionFound: true,
      roomEmptyAfterRemoval: roomConnections.size === 0
    });
  }

  return removedConnection;
}

/**
 * 获取房间内的所有连接（带数据库回退）
 * @param {string} roomId 房间ID
 * @returns {Promise<Set<ConnectionInfo>>} 房间连接集合
 */
async function getRoomConnections(roomId) {
  Logger.debug('获取房间连接', { roomId }, 'CONNECTION_POOL');
  
  // 首先尝试从内存获取
  // let roomConnections = roomConnectionPools.get(roomId);
  // console.log(roomConnections,'roomConnections-storage')
  // if (!roomConnections || roomConnections.size === 0) {
    Logger.info('内存中无连接数据，尝试从数据库恢复', { roomId }, 'CONNECTION_POOL');
    
    try {
      // 从数据库恢复连接数据
      roomConnections = await restoreRoomConnectionsFromDatabase(roomId);
      console.log(roomConnections,'roomConnections-database')
      if (roomConnections.size > 0) {
        // 将恢复的连接存储到内存中
        roomConnectionPools.set(roomId, roomConnections);
        Logger.info('从数据库恢复连接成功', {
          roomId,
          recoveredConnections: roomConnections.size
        }, 'CONNECTION_POOL');
      }
    } catch (error) {
      Logger.error('从数据库恢复连接失败', error, 'CONNECTION_POOL');
      roomConnections = new Set(); // 返回空集合
    }
  // }
  console.log(roomConnections,'roomConnections')
  return roomConnections || new Set();
}

/**
 * 更新连接的心跳时间
 * @param {string} roomId 房间ID
 * @param {string} connectionId 连接ID
 */
async function updateConnectionHeartbeat(roomId, connectionId) {
  const roomConnections = await getRoomConnections(roomId);
  for (const conn of roomConnections) {
    if (conn.connectionId === connectionId) {
      conn.updateHeartbeat();
      break;
    }
  }
}

/**
 * 清理过期的连接
 */
function cleanupExpiredConnections() {
  const startTime = Date.now();
  let totalCleaned = 0;
  let roomsCleaned = 0;
  const cleanupDetails = [];
  
  Logger.debug('开始清理过期连接', {
    totalRooms: roomConnectionPools.size,
    totalConnections: Array.from(roomConnectionPools.values()).reduce((sum, set) => sum + set.size, 0)
  }, 'CONNECTION_CLEANUP');
  
  for (const [roomId, roomConnections] of roomConnectionPools) {
    const expiredConnections = [];
    const roomStartSize = roomConnections.size;
    
    for (const conn of roomConnections) {
      if (conn.isExpired()) {
        expiredConnections.push(conn);
      }
    }
    
    // 移除过期连接
    expiredConnections.forEach(conn => {
      roomConnections.delete(conn);
      totalCleaned++;
      
      const expiredDuration = Date.now() - conn.lastHeartbeat.getTime();
      Logger.info('清理过期连接', {
        connectionId: conn.connectionId,
        userId: conn.userId,
        nickname: conn.nickname,
        roomId,
        expiredDuration,
        connectionAge: Date.now() - conn.joinTime.getTime()
      }, 'CONNECTION_CLEANUP');
    });
    
    // 清理空房间
    if (roomConnections.size === 0 && roomStartSize > 0) {
      roomConnectionPools.delete(roomId);
      roomsCleaned++;
      Logger.info('清理空房间', { roomId }, 'CONNECTION_CLEANUP');
    }
    
    if (expiredConnections.length > 0) {
      cleanupDetails.push({
        roomId,
        expiredCount: expiredConnections.length,
        remainingCount: roomConnections.size
      });
    }
  }
  
  const duration = Date.now() - startTime;
  
  if (totalCleaned > 0 || roomsCleaned > 0) {
    Logger.info('连接池清理完成', {
      expiredConnections: totalCleaned,
      cleanedRooms: roomsCleaned,
      remainingRooms: roomConnectionPools.size,
      duration,
      details: cleanupDetails
    }, 'CONNECTION_CLEANUP');
    
    Logger.performance('cleanupExpiredConnections', duration, {
      connectionsRemoved: totalCleaned,
      roomsRemoved: roomsCleaned
    });
  } else {
    Logger.debug('连接池清理完成，无过期连接', {
      totalRooms: roomConnectionPools.size,
      duration
    }, 'CONNECTION_CLEANUP');
  }
}

/**
 * 获取连接池统计信息
 */
function getConnectionPoolStats() {
  let totalConnections = 0;
  const roomStats = {};
  
  for (const [roomId, roomConnections] of roomConnectionPools) {
    roomStats[roomId] = roomConnections.size;
    totalConnections += roomConnections.size;
  }
  
  return {
    totalRooms: roomConnectionPools.size,
    totalConnections,
    roomStats
  };
}

// ==================== 数据库连接状态同步 ====================

/**
 * 将连接状态同步到数据库
 * @param {string} connectionId 连接ID
 * @param {string} roomId 房间ID
 * @param {string} userId 用户ID
 * @param {string} nickname 用户昵称
 * @param {string} avatarFileId 用户头像文件ID
 */
async function syncConnectionToDatabase(connectionId, roomId, userId, nickname, avatarFileId) {
  const startTime = Date.now();
  
  try {
    Logger.debug('开始同步连接状态到数据库', {
      connectionId,
      roomId,
      userId,
      nickname
    }, 'DB_SYNC');

    const db = uniCloud.database();
    const connectionsCollection = db.collection("websocket_connections");
    
    const connectionData = {
      connection_id: connectionId,
      room_id: roomId,
      user_id: userId,
      nickname: nickname,
      avatar_fileId: avatarFileId,
      connect_time: new Date(),
      last_heartbeat: new Date(),
      status: 'active'
    };
    
    // 先删除该用户在该房间的旧连接记录
    const removeStartTime = Date.now();
    const removeResult = await connectionsCollection.where({
      room_id: roomId,
      user_id: userId
    }).remove();
    
    Logger.database('REMOVE', 'websocket_connections', 
      { room_id: roomId, user_id: userId }, 
      removeResult, 
      Date.now() - removeStartTime
    );
    
    // 添加新的连接记录
    const addStartTime = Date.now();
    const addResult = await connectionsCollection.add(connectionData);
    
    Logger.database('ADD', 'websocket_connections', 
      connectionData, 
      addResult, 
      Date.now() - addStartTime
    );
    
    const totalDuration = Date.now() - startTime;
    Logger.info('连接状态已同步到数据库', {
      connectionId,
      userId,
      nickname,
      roomId,
      removedOldRecords: removeResult.deleted || 0,
      newRecordId: addResult.id,
      duration: totalDuration
    }, 'DB_SYNC');
    
    Logger.performance('syncConnectionToDatabase', totalDuration, {
      roomId,
      hadOldRecords: (removeResult.deleted || 0) > 0
    });
    
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('同步连接状态到数据库失败', error, 'DB_SYNC');
    Logger.performance('syncConnectionToDatabase', duration, { success: false });
  }
}

/**
 * 从数据库移除连接状态
 * @param {string} connectionId 连接ID
 * @param {string} reason 断开原因：normal, timeout, error
 */
async function removeConnectionFromDatabase(connectionId, reason = 'normal') {
  try {
    const db = uniCloud.database();
    const connectionsCollection = db.collection("websocket_connections");
    
    // 更新连接状态为过期，记录断开时间和原因
    const result = await connectionsCollection.where({
      connection_id: connectionId,
      status: 'active'
    }).update({
      status: 'expired',
      disconnect_time: new Date(),
      disconnect_reason: reason
    });
    
    console.log(`连接状态已更新为过期: ${connectionId}, 原因: ${reason}, 更新记录数: ${result.updated}`);
  } catch (error) {
    console.error('更新数据库连接状态失败:', error);
  }
}

/**
 * 更新数据库中的连接心跳时间
 * @param {string} connectionId 连接ID
 */
async function updateConnectionHeartbeatInDatabase(connectionId) {
  try {
    const db = uniCloud.database();
    const connectionsCollection = db.collection("websocket_connections");
    
    await connectionsCollection.where({
      connection_id: connectionId
    }).update({
      last_heartbeat: new Date()
    });
    
    // 不记录日志，避免频繁输出
  } catch (error) {
    console.error('更新数据库连接心跳失败:', error);
  }
}

/**
 * 从数据库恢复特定房间的连接状态到内存
 * @param {string} roomId 房间ID
 * @returns {Promise<Set<ConnectionInfo>>} 恢复的连接集合
 */
async function restoreRoomConnectionsFromDatabase(roomId) {
  const startTime = Date.now();
  
  try {
    Logger.debug('开始从数据库恢复房间连接', { roomId }, 'DB_RESTORE');
    
    const db = uniCloud.database();
    const connectionsCollection = db.collection("websocket_connections");
    
    // 获取指定房间的所有活跃连接
    const connectionsRes = await connectionsCollection.where({
      room_id: roomId,
      status: 'active'
    }).orderBy('last_heartbeat', 'desc').get();
    
    Logger.database('SELECT', 'websocket_connections', 
      { room_id: roomId, status: 'active' }, 
      { total: connectionsRes.data.length }, 
      Date.now() - startTime
    );
    
    const restoredConnections = new Set();
    const expiredConnectionIds = [];
    const maxAge = 30 * 60 * 1000; // 30分钟
    
    for (const conn of connectionsRes.data) {
      const { connection_id, user_id, nickname, avatar_fileId, connect_time, last_heartbeat } = conn;
      
      // 检查连接是否仍然有效
      const connectionAge = Date.now() - new Date(last_heartbeat).getTime();
      
      if (connectionAge < maxAge) {
        // 创建 ConnectionInfo 对象，确保数据格式一致
        const connectionInfo = new ConnectionInfo(connection_id, user_id, nickname, avatar_fileId);
        
        // 设置正确的时间戳（从数据库恢复）
        connectionInfo.joinTime = new Date(connect_time);
        connectionInfo.lastHeartbeat = new Date(last_heartbeat);
        
        restoredConnections.add(connectionInfo);
        
        Logger.debug('恢复连接记录', {
          connectionId: connection_id,
          userId: user_id,
          nickname,
          connectionAge,
          joinTime: connectionInfo.joinTime.toISOString(),
          lastHeartbeat: connectionInfo.lastHeartbeat.toISOString()
        }, 'DB_RESTORE');
      } else {
        // 记录过期的连接，稍后批量清理
        expiredConnectionIds.push(conn._id);
        
        Logger.warn('发现过期连接记录', {
          connectionId: connection_id,
          userId: user_id,
          nickname,
          connectionAge,
          maxAge
        }, 'DB_RESTORE');
      }
    }
    
    // 批量清理过期连接
    if (expiredConnectionIds.length > 0) {
      try {
        await connectionsCollection.where({
          _id: db.command.in(expiredConnectionIds)
        }).update({
          status: 'expired',
          disconnect_time: new Date(),
          disconnect_reason: 'timeout'
        });
        
        Logger.info('批量清理过期连接记录', {
          roomId,
          expiredCount: expiredConnectionIds.length
        }, 'DB_RESTORE');
      } catch (cleanupError) {
        Logger.error('清理过期连接记录失败', cleanupError, 'DB_RESTORE');
      }
    }
    
    const totalDuration = Date.now() - startTime;
    Logger.info('房间连接恢复完成', {
      roomId,
      totalRecords: connectionsRes.data.length,
      restoredConnections: restoredConnections.size,
      expiredConnections: expiredConnectionIds.length,
      duration: totalDuration
    }, 'DB_RESTORE');
    
    Logger.performance('restoreRoomConnectionsFromDatabase', totalDuration, {
      roomId,
      restoredCount: restoredConnections.size,
      expiredCount: expiredConnectionIds.length
    });
    
    return restoredConnections;
    
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('从数据库恢复房间连接失败', error, 'DB_RESTORE');
    Logger.performance('restoreRoomConnectionsFromDatabase', duration, { 
      success: false,
      roomId 
    });
    return new Set();
  }
}

/**
 * 从数据库恢复连接状态到内存
 * 用于云函数重启后的状态恢复
 */
async function restoreConnectionsFromDatabase() {
  try {
    const db = uniCloud.database();
    const connectionsCollection = db.collection("websocket_connections");
    
    // 获取所有活跃连接
    const connectionsRes = await connectionsCollection.where({
      status: 'active'
    }).get();
    
    let restoredCount = 0;
    
    for (const conn of connectionsRes.data) {
      const { connection_id, room_id, user_id, nickname, avatar_fileId } = conn;
      
      // 检查连接是否仍然有效（这里简化处理，实际可能需要ping测试）
      const connectionAge = Date.now() - new Date(conn.last_heartbeat).getTime();
      const maxAge = 30 * 60 * 1000; // 30分钟
      
      if (connectionAge < maxAge) {
        // 恢复到内存连接池
        if (addConnectionToRoom(room_id, connection_id, user_id, nickname, avatar_fileId)) {
          restoredCount++;
        }
      } else {
        // 将过期的连接标记为过期状态
        await connectionsCollection.doc(conn._id).update({
          status: 'expired',
          disconnect_time: new Date(),
          disconnect_reason: 'timeout'
        });
        console.log(`标记过期的数据库连接记录: ${connection_id}`);
      }
    }
    
    console.log(`从数据库恢复连接状态完成，恢复连接数: ${restoredCount}`);
  } catch (error) {
    console.error('从数据库恢复连接状态失败:', error);
  }
}

/**
 * 清理数据库中的过期连接记录
 */
async function cleanupExpiredConnectionsInDatabase() {
  try {
    const db = uniCloud.database();
    const connectionsCollection = db.collection("websocket_connections");
    
    const expireTime = new Date(Date.now() - 30 * 60 * 1000); // 30分钟前
    
    // 将过期的活跃连接标记为过期状态
    const result = await connectionsCollection.where({
      last_heartbeat: db.command.lt(expireTime),
      status: 'active'
    }).update({
      status: 'expired',
      disconnect_time: new Date(),
      disconnect_reason: 'timeout'
    });
    
    if (result.updated > 0) {
      console.log(`清理数据库过期连接记录完成，更新记录数: ${result.updated}`);
    }

    // 可选：删除很久以前的过期记录（比如7天前的）
    const oldExpireTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7天前
    const deleteResult = await connectionsCollection.where({
      disconnect_time: db.command.lt(oldExpireTime),
      status: 'expired'
    }).remove();
    
    if (deleteResult.deleted > 0) {
      console.log(`删除历史过期连接记录: ${deleteResult.deleted} 条`);
    }
    
  } catch (error) {
    console.error('清理数据库过期连接记录失败:', error);
  }
}

/**
 * 获取房间的数据库连接统计
 * @param {string} roomId 房间ID
 */
async function getRoomConnectionStatsFromDatabase(roomId) {
  try {
    const db = uniCloud.database();
    const connectionsCollection = db.collection("websocket_connections");
    
    const result = await connectionsCollection.where({
      room_id: roomId,
      status: 'active'
    }).count();
    
    return result.total;
  } catch (error) {
    console.error('获取房间数据库连接统计失败:', error);
    return 0;
  }
}

// ==================== 消息广播核心函数 ====================

/**
 * 向房间内所有连接广播消息
 * @param {string} roomId 房间ID
 * @param {Object} message 广播消息
 * @param {string} excludeConnectionId 排除的连接ID（通常是操作发起者）
 * @returns {Promise<Object>} 广播结果 { success: boolean, sentCount: number, failedCount: number }
 */
async function broadcastToRoom(roomId, message, excludeConnectionId = null) {
  const startTime = Date.now();
  const messageType = message.operationType || message.action || 'unknown';
  
  try {
    Logger.debug('开始房间广播', {
      roomId,
      messageType,
      excludeConnectionId,
      messageSize: JSON.stringify(message).length
    }, 'BROADCAST');

    const roomConnections = await getRoomConnections(roomId);
    
    if (roomConnections.size === 0) {
      Logger.info('房间没有活跃连接，跳过广播', {
        roomId,
        messageType
      }, 'BROADCAST');
      return { success: true, sentCount: 0, failedCount: 0 };
    }

    const ws = uniCloud.webSocketServer();
    
    // 准备广播消息
    const broadcastMessage = {
      ...message,
      action: 'room_broadcast',
      timestamp: Date.now()
    };

    // 收集目标连接ID和连接信息
    const targetConnectionIds = [];
    const connectionInfoMap = new Map();
    let targetConnections = 0;

    for (const conn of roomConnections) {
      if (excludeConnectionId && conn.connectionId === excludeConnectionId) {
        Logger.debug('跳过排除的连接', {
          connectionId: conn.connectionId,
          userId: conn.userId,
          nickname: conn.nickname
        }, 'BROADCAST');
        continue; // 跳过排除的连接
      }

      targetConnections++;
      targetConnectionIds.push(conn.connectionId);
      connectionInfoMap.set(conn.connectionId, conn);
    }

    if (targetConnectionIds.length === 0) {
      Logger.info('没有目标连接，跳过广播', {
        roomId,
        messageType,
        totalConnections: roomConnections.size,
        excludedConnections: excludeConnectionId ? 1 : 0
      }, 'BROADCAST');
      return { success: true, sentCount: 0, failedCount: 0, targetConnections: 0 };
    }

    Logger.debug('准备批量广播消息', {
      roomId,
      totalConnections: roomConnections.size,
      targetConnections,
      messageType,
      broadcastMessageSize: JSON.stringify(broadcastMessage).length
    }, 'BROADCAST');

    // 使用批量发送
    try {
      await ws.send(targetConnectionIds, JSON.stringify(broadcastMessage));
      
      // 批量更新心跳时间
      for (const connectionId of targetConnectionIds) {
        const conn = connectionInfoMap.get(connectionId);
        if (conn) {
          conn.updateHeartbeat();
        }
      }

      const duration = Date.now() - startTime;
      
      Logger.broadcast(roomId, messageType, targetConnections, targetConnections, 0, {
        duration,
        excludedConnections: excludeConnectionId ? 1 : 0,
        messageSize: JSON.stringify(broadcastMessage).length,
        batchSend: true
      });
      
      Logger.performance('broadcastToRoom', duration, {
        roomId,
        messageType,
        targetConnections,
        successRate: '100%',
        batchSend: true
      });
      
      return {
        success: true,
        sentCount: targetConnections,
        failedCount: 0,
        targetConnections
      };

    } catch (batchError) {
      // 批量发送失败，降级到逐个发送以获得详细错误信息
      Logger.warn('批量发送失败，降级到逐个发送', {
        roomId,
        messageType,
        targetConnections,
        error: batchError.message
      }, 'BROADCAST');
      
      const broadcastPromises = [];
      
      for (const connectionId of targetConnectionIds) {
        const conn = connectionInfoMap.get(connectionId);
        
        const sendPromise = ws.send(connectionId, JSON.stringify(broadcastMessage))
          .then(() => {
            // 更新心跳时间
            if (conn) {
              conn.updateHeartbeat();
            }
            Logger.debug('广播消息发送成功', {
              connectionId,
              userId: conn?.userId,
              nickname: conn?.nickname,
              messageType
            }, 'BROADCAST');
            return { connectionId, success: true };
          })
          .catch(error => {
            Logger.warn('向连接发送广播消息失败', {
              connectionId,
              userId: conn?.userId,
              nickname: conn?.nickname,
              messageType,
              error: error.message
            }, 'BROADCAST');
            return { connectionId, success: false, error };
          });

        broadcastPromises.push(sendPromise);
      }

      // 等待所有发送完成
      const results = await Promise.all(broadcastPromises);
      
      // 统计结果
      let sentCount = 0;
      let failedCount = 0;
      const failedConnections = [];

      results.forEach(result => {
        if (result.success) {
          sentCount++;
        } else {
          failedCount++;
          failedConnections.push(result.connectionId);
        }
      });

      // 清理失败的连接
      if (failedConnections.length > 0) {
        Logger.info('开始清理失败的连接', {
          roomId,
          failedConnections: failedConnections.length,
          failedConnectionIds: failedConnections
        }, 'BROADCAST');
        await cleanupFailedConnections(roomId, failedConnections);
      }

      const duration = Date.now() - startTime;
      
      Logger.broadcast(roomId, messageType, targetConnections, sentCount, failedCount, {
        duration,
        excludedConnections: excludeConnectionId ? 1 : 0,
        messageSize: JSON.stringify(broadcastMessage).length,
        fallbackToIndividual: true
      });
      
      Logger.performance('broadcastToRoom', duration, {
        roomId,
        messageType,
        targetConnections,
        successRate: targetConnections > 0 ? (sentCount / targetConnections * 100).toFixed(1) + '%' : '100%',
        fallbackToIndividual: true
      });
      
      return {
        success: true,
        sentCount,
        failedCount,
        targetConnections
      };
    }

  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('房间广播异常', error, 'BROADCAST');
    Logger.performance('broadcastToRoom', duration, { 
      success: false,
      roomId,
      messageType 
    });
    
    return {
      success: false,
      sentCount: 0,
      failedCount: 0,
      error: error.message
    };
  }
}

/**
 * 向房间广播操作结果（区分操作者和其他玩家）
 * @param {string} roomId 房间ID
 * @param {string} operatorConnectionId 操作发起者连接ID
 * @param {Object} operatorResponse 发给操作者的完整响应
 * @param {Object} broadcastData 发给其他玩家的广播数据
 */
async function broadcastOperationResult(roomId, operatorConnectionId, operatorResponse, broadcastData) {
  try {
    const ws = uniCloud.webSocketServer();
    
    // 1. 向操作发起者发送完整响应
    try {
      await ws.send(operatorConnectionId, JSON.stringify(operatorResponse));
      console.log(`操作响应已发送给发起者: ${operatorConnectionId}`);
      
      // 更新操作者的心跳时间
      await updateConnectionHeartbeat(roomId, operatorConnectionId);
    } catch (error) {
      console.error(`向操作发起者发送响应失败:`, error);
      // 清理失败的连接
      await cleanupFailedConnections(roomId, [operatorConnectionId]);
    }

    // 2. 向房间其他玩家广播操作通知
    const broadcastResult = await broadcastToRoom(roomId, broadcastData, operatorConnectionId);
    
    console.log(`操作广播完成 - 房间: ${roomId}, 广播给 ${broadcastResult.sentCount} 个其他玩家`);
    
    return {
      success: true,
      operatorNotified: true,
      broadcastResult
    };

  } catch (error) {
    console.error('广播操作结果异常:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 清理失败的连接
 * @param {string} roomId 房间ID
 * @param {Array<string>} failedConnectionIds 失败的连接ID数组
 */
async function cleanupFailedConnections(roomId, failedConnectionIds) {
  try {
    for (const connectionId of failedConnectionIds) {
      // 从内存连接池移除
      const removedConnection = removeConnectionFromRoom(roomId, connectionId);
      
      // 从数据库移除
      await removeConnectionFromDatabase(connectionId, 'error');
      
      if (removedConnection) {
        console.log(`清理失败连接: ${connectionId} (用户: ${removedConnection.nickname})`);
        
        // 通知房间其他玩家该用户离线
        await broadcastToRoom(roomId, {
          action: 'player_offline',
          data: {
            userId: removedConnection.userId,
            nickname: removedConnection.nickname,
            reason: 'connection_failed'
          }
        });
      }
    }
  } catch (error) {
    console.error('清理失败连接异常:', error);
  }
}

/**
 * 向特定连接发送消息（带重试机制）
 * @param {string} connectionId 连接ID
 * @param {Object} message 消息内容
 * @param {number} maxRetries 最大重试次数
 * @returns {Promise<boolean>} 发送是否成功
 */
async function sendMessageWithRetry(connectionId, message, maxRetries = 3) {
  const ws = uniCloud.webSocketServer();
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await ws.send(connectionId, JSON.stringify(message));
      return true;
    } catch (error) {
      console.error(`向连接 ${connectionId} 发送消息失败 (尝试 ${attempt}/${maxRetries}):`, error);
      
      if (attempt === maxRetries) {
        return false;
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 100 * attempt));
    }
  }
  
  return false;
}

/**
 * 批量发送消息
 * @param {Array<{connectionId: string, message: Object}>} messageList 消息列表
 * @returns {Promise<Object>} 发送结果统计
 */
async function batchSendMessages(messageList) {
  if (!messageList || messageList.length === 0) {
    return {
      total: 0,
      success: 0,
      failed: 0,
      failedConnections: []
    };
  }

  const ws = uniCloud.webSocketServer();
  
  // 检查是否所有消息内容都相同，如果相同则可以使用真正的批量发送
  const firstMessage = messageList[0].message;
  const sameMessage = messageList.every(item => 
    JSON.stringify(item.message) === JSON.stringify(firstMessage)
  );

  if (sameMessage && messageList.length > 1) {
    // 所有消息内容相同，使用批量发送
    Logger.debug('批量发送相同消息', {
      messageCount: messageList.length,
      messageSize: JSON.stringify(firstMessage).length
    }, 'BATCH_SEND');
    
    const connectionIds = messageList.map(item => item.connectionId);
    
    try {
      await ws.send(connectionIds, JSON.stringify(firstMessage));
      
      Logger.info('批量发送消息成功', {
        total: messageList.length,
        success: messageList.length,
        failed: 0,
        batchSend: true
      }, 'BATCH_SEND');
      
      return {
        total: messageList.length,
        success: messageList.length,
        failed: 0,
        failedConnections: []
      };
      
    } catch (batchError) {
      // 批量发送失败，降级到逐个发送
      Logger.warn('批量发送失败，降级到逐个发送', {
        messageCount: messageList.length,
        error: batchError.message
      }, 'BATCH_SEND');
    }
  }

  // 消息内容不同或批量发送失败，使用逐个发送
  Logger.debug('使用逐个发送模式', {
    messageCount: messageList.length,
    reason: sameMessage ? 'batch_send_failed' : 'different_messages'
  }, 'BATCH_SEND');
  
  const sendPromises = messageList.map(async ({ connectionId, message }) => {
    const success = await sendMessageWithRetry(connectionId, message);
    return { connectionId, success };
  });
  
  const results = await Promise.all(sendPromises);
  
  const stats = {
    total: results.length,
    success: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    failedConnections: results.filter(r => !r.success).map(r => r.connectionId)
  };
  
  Logger.info('批量发送消息完成', {
    total: stats.total,
    success: stats.success,
    failed: stats.failed,
    failedConnections: stats.failedConnections.length,
    individualSend: true
  }, 'BATCH_SEND');
  
  return stats;
}

/**
 * 验证JWT token
 * @param {string} token JWT token
 * @returns {Object} 验证结果 {valid: boolean, payload: Object, error: string}
 */
function verifyToken(token) {
  try {
    if (!token) {
      return { valid: false, error: "Token为空" };
    }

    const options = {
      issuer: "suixin-scorer",
    };

    const payload = jwt.verify(token, JWT_SECRET, options);
    return { valid: true, payload };
  } catch (error) {
    let errorMessage = "Token验证失败";
    if (error.name === "TokenExpiredError") {
      errorMessage = "Token已过期";
    } else if (error.name === "JsonWebTokenError") {
      errorMessage = "Token格式错误";
    } else if (error.name === "NotBeforeError") {
      errorMessage = "Token未生效";
    }

    return { valid: false, error: errorMessage };
  }
}

/**
 * 生成请求ID
 * @returns {string} UUID格式的请求ID
 */
function generateRequestId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 验证token并获取用户信息
 * @param {string} token JWT token
 * @param {Object} usersCollection 用户集合
 * @returns {Object} 验证结果
 */
async function verifyTokenAndGetUser(token, usersCollection) {
  const verifyResult = verifyToken(token);
  if (!verifyResult.valid) {
    return {
      success: false,
      message: verifyResult.error,
    };
  }

  const userRes = await usersCollection
    .where({ wx_openid: verifyResult.payload.openId })
    .get();

  if (userRes.data.length === 0) {
    return {
      success: false,
      message: "用户不存在",
    };
  }

  return {
    success: true,
    data: {
      openId: verifyResult.payload.openId,
      userInfo: userRes.data[0],
    },
  };
}

/**
 * 格式化时间戳
 * @param {Date} timestamp 时间戳
 * @returns {string} 格式化后的时间字符串
 */
function formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
}

/**
 * 计算茶水抽佣
 * @param {number} originalScore 原始分数
 * @param {number} teaRatio 茶水比例 (0-100)
 * @param {number} currentBalance 当前茶水余额
 * @param {number} limitAmount 抽取上限
 * @returns {Object} { teaAmount: 抽取的茶水金额, actualAmount: 玩家实际得到的分数 }
 */
function calculateTeaDeduction(originalScore, teaRatio, currentBalance, limitAmount) {
  if (!teaRatio || teaRatio <= 0 || originalScore <= 0) {
    return { teaAmount: 0, actualAmount: originalScore };
  }

  // 计算基础茶水抽取金额
  let teaAmount = NP.divide(NP.times(originalScore, teaRatio), 100);

  // 检查抽取上限
  if (limitAmount && limitAmount > 0) {
    const potentialBalance = NP.plus(currentBalance, teaAmount);
    if (potentialBalance > limitAmount) {
      teaAmount = Math.max(0, NP.minus(limitAmount, currentBalance));
    }
  }

  // 确保茶水金额不超过原始分数
  teaAmount = Math.min(teaAmount, originalScore);

  const actualAmount = NP.minus(originalScore, teaAmount);

  return {
    teaAmount: NP.round(teaAmount, 2),
    actualAmount: NP.round(actualAmount, 2)
  };
}

/**
 * 合并相同玩家的分数变化
 * @param {Array} playerUpdates 玩家更新数组 [{ id, scoreChange }]
 * @returns {Array} 合并后的玩家更新数组
 */
function consolidatePlayerUpdates(playerUpdates) {
  const playerMap = new Map();
  
  playerUpdates.forEach(update => {
    const { id, scoreChange } = update;
    if (playerMap.has(id)) {
      playerMap.set(id, NP.plus(playerMap.get(id), scoreChange));
    } else {
      playerMap.set(id, scoreChange);
    }
  });
  
  return Array.from(playerMap.entries()).map(([id, scoreChange]) => ({
    id,
    scoreChange: NP.round(scoreChange, 2)
  }));
}

/**
 * 获取当前局的桌面分数
 * @param {string} roomId 房间ID
 * @param {number} currentRound 当前局数
 * @returns {Promise<number>} 当前桌面分数
 */
async function getCurrentTableScore(roomId, currentRound) {
  try {
    const db = uniCloud.database();
    const messagesCollection = db.collection("room_messages");
    
    // 查询当前局的所有出分和收分消息
    const messagesRes = await messagesCollection
      .where({
        room_id: roomId,
        message_type: db.command.in(['distribute', 'collect']),
        'detail_data.round_number': currentRound
      })
      .orderBy('timestamp', 'asc')
      .get();
    
    let tableScore = 0;
    
    // 计算桌面分数：出分增加，收分减少
    messagesRes.data.forEach(message => {
      const { message_type, detail_data } = message;
      const amount = detail_data.amount || detail_data.original_amount || 0;
      
      if (message_type === 'distribute') {
        tableScore = NP.plus(tableScore, amount);
      } else if (message_type === 'collect') {
        tableScore = NP.minus(tableScore, amount);
      }
    });
    
    return NP.round(tableScore, 2);
  } catch (error) {
    console.error('获取当前桌面分数失败:', error);
    return 0;
  }
}

/**
 * 检查用户是否为新加入用户
 * @param {string} userId 用户ID
 * @param {string} roomId 房间ID
 * @param {Object} room 房间数据
 * @returns {boolean} 是否为新加入用户
 */
function checkIfNewJoiner(userId, roomId, room) {
  try {
    // 查找用户在房间中的信息
    const player = room.players.find(p => p.user_id === userId);
    
    if (!player) {
      // 用户不在房间中，说明是全新用户加入
      Logger.debug('新用户检测结果: 全新用户', {
        userId,
        roomId,
        reason: 'player_not_found'
      }, 'NEW_JOINER_CHECK');
      return true;
    }
    
    // 如果用户在房间中，且 has_left 为 true，说明是重新加入
    if (player.has_left === true) {
      Logger.debug('新用户检测结果: 重新加入', {
        userId,
        roomId,
        playerName: player.nickname,
        joinTime: player.join_time,
        reason: 'player_returning'
      }, 'NEW_JOINER_CHECK');
      return true;
    }
    
    // 如果用户在房间中，且 has_left 不为 true，说明用户还在房间中，这是重连
    Logger.debug('新用户检测结果: 重连', {
      userId,
      roomId,
      playerName: player.nickname,
      joinTime: player.join_time,
      hasLeft: player.has_left,
      reason: 'player_reconnect'
    }, 'NEW_JOINER_CHECK');
    return false;
    
  } catch (error) {
    Logger.error('检查新用户失败', error, 'NEW_JOINER_CHECK');
    // 出错时默认为非新用户，使用普通上线流程
    return false;
  }
}

/**
 * 处理新用户加入通知
 * @param {string} roomId 房间ID
 * @param {Object} userInfo 用户信息
 * @param {string} connectionId 连接ID
 * @param {number} onlineCount 在线人数
 */
async function handleNewUserJoinNotification(roomId, userInfo, connectionId, onlineCount) {
  try {
    const db = uniCloud.database();
    
    // 1. 生成系统消息
    const messageData = {
      room_id: roomId,
      message_type: "system",
      sender_id: userInfo._id,
      sender_name: userInfo.nickname,
      sender_avatar_fileId: userInfo.avatar_fileId,
      timestamp: new Date(),
      detail_data: {
        message_text: `${userInfo.nickname} 加入了房间`,
        action_type: "player_join_websocket"
      }
    };

    // 添加系统消息到数据库
    const addResult = await db.collection("room_messages").add(messageData);
    
    Logger.info('新用户加入系统消息已生成', {
      messageId: addResult.id,
      userId: userInfo._id,
      nickname: userInfo.nickname,
      roomId
    }, 'NEW_JOINER');

    // 2. 准备完整的新用户信息
    const newPlayerInfo = {
      id: userInfo._id,
      name: userInfo.nickname,
      avatar: userInfo.avatar_fileId,
      score: 0,
      hasLeft: false,
      isNewJoin: true
    };

    // 3. 向房间其他玩家广播新用户加入通知（包含完整信息）
    await broadcastToRoom(roomId, {
      action: 'player_join_notification',
      data: {
        operationType: 'player_join_websocket',
        newPlayer: newPlayerInfo,
        onlineCount,
        incremental: {
          newPlayer: newPlayerInfo,
          newMessage: {
            id: addResult.id,
            type: "system",
            timestamp: formatTimestamp(messageData.timestamp),
            sender: {
              id: userInfo._id,
              name: userInfo.nickname,
              avatar_fileId: userInfo.avatar_fileId
            },
            message: messageData.detail_data.message_text,
            actionType: messageData.detail_data.action_type,
            isSelf: false
          }
        }
      }
    }, connectionId);

    // 4. 同时向新用户发送欢迎消息
    const ws = uniCloud.webSocketServer();
    await ws.send(connectionId, JSON.stringify({
      action: 'welcome_message',
      success: true,
      data: {
        message: '欢迎加入房间！',
        systemMessage: {
          id: addResult.id,
          type: "system",
          timestamp: formatTimestamp(messageData.timestamp),
          sender: {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar_fileId: userInfo.avatar_fileId
          },
          message: messageData.detail_data.message_text,
          actionType: messageData.detail_data.action_type,
          isSelf: true
        }
      },
      timestamp: Date.now()
    }));

    Logger.business('新用户加入通知处理完成', userInfo._id, roomId, {
      messageId: addResult.id,
      onlineCount,
      broadcastSuccess: true
    });

  } catch (error) {
    Logger.error('处理新用户加入通知失败', error, 'NEW_JOINER');
    
    // 失败时降级为普通上线通知
    await broadcastToRoom(roomId, {
      action: 'player_online',
      data: {
        userId: userInfo._id,
        nickname: userInfo.nickname,
        avatar: userInfo.avatar_fileId,
        onlineCount
      }
    }, connectionId);
  }
}

/**
 * 发送WebSocket响应
 * @param {string} connectionId 连接ID
 * @param {string} action 操作类型
 * @param {boolean} success 是否成功
 * @param {Object} data 响应数据
 * @param {string} requestId 请求ID
 * @param {string} message 错误信息
 */
async function sendResponse(connectionId, action, success, data = null, requestId = null, message = null) {
  try {
    const ws = uniCloud.webSocketServer();
    const response = {
      action: `${action}_result`,
      success,
      data,
      requestId,
      timestamp: Date.now(),
      message
    };
    
    await ws.send(connectionId, JSON.stringify(response));
    console.log(`WebSocket响应已发送: ${action}, 成功: ${success}`);
  } catch (error) {
    console.error('发送WebSocket响应失败:', error);
  }
}

// WebSocket连接事件
exports.onWebsocketConnection = async function (event, context) {
  const startTime = Date.now();
  const { connectionId, query } = event;
  
  Logger.connection('CONNECT_ATTEMPT', connectionId, null, query?.roomId, {
    query: Logger.sanitizeData(query),
    context: {
      requestId: context.requestId,
      source: context.source
    }
  });
  
  try {
    // 验证必要参数
    if (!query || !query.token || !query.roomId) {
      const error = new Error('WebSocket连接参数不完整');
      Logger.error('WebSocket连接参数验证失败', error, 'CONNECTION', connectionId);
      Logger.connection('FAILED', connectionId, null, null, {
        reason: 'invalid_parameters',
        query: Logger.sanitizeData(query),
        duration: Date.now() - startTime
      });
      return;
    }

    const { token, roomId } = query;
    
    Logger.debug('WebSocket连接参数验证通过', {
      connectionId,
      roomId,
      hasToken: !!token
    }, 'CONNECTION');

    // 初始化数据库连接
    const db = uniCloud.database();
    const usersCollection = db.collection("users");
    const roomsCollection = db.collection("rooms");

    // 验证用户token并获取用户信息
    const authStartTime = Date.now();
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    Logger.performance('userTokenVerification', Date.now() - authStartTime);
    
    if (!userVerifyResult.success) {
      const error = new Error(`用户验证失败: ${userVerifyResult.message}`);
      Logger.error('WebSocket连接用户验证失败', error, 'CONNECTION', connectionId);
      
      const ws = uniCloud.webSocketServer();
      await ws.send(connectionId, JSON.stringify({
        action: 'connection_error',
        success: false,
        message: userVerifyResult.message,
        timestamp: Date.now()
      }));
      
      await ws.close(connectionId);
      Logger.connection('FAILED', connectionId, null, roomId, {
        reason: 'auth_failed',
        duration: Date.now() - startTime
      });
      return;
    }

    const { userInfo } = userVerifyResult.data;
    Logger.debug('用户验证成功', {
      userId: userInfo._id,
      nickname: userInfo.nickname,
      connectionId,
      roomId
    }, 'CONNECTION');

    // 验证房间存在且用户有权限
    const roomVerifyStartTime = Date.now();
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    Logger.performance('roomVerification', Date.now() - roomVerifyStartTime);
    
    if (roomRes.data.length === 0) {
      const error = new Error(`房间不存在: ${roomId}`);
      Logger.error('WebSocket连接房间验证失败', error, 'CONNECTION', connectionId);
      
      const ws = uniCloud.webSocketServer();
      await ws.send(connectionId, JSON.stringify({
        action: 'connection_error',
        success: false,
        message: '房间不存在',
        timestamp: Date.now()
      }));
      
      await ws.close(connectionId);
      Logger.connection('FAILED', connectionId, userInfo, roomId, {
        reason: 'room_not_found',
        duration: Date.now() - startTime
      });
      return;
    }

    const room = roomRes.data[0];
    const userInRoom = room.players.some(p => p.user_id === userInfo._id && p.has_left !== true);
    if (!userInRoom) {
      const error = new Error(`用户无权限访问房间: ${userInfo.nickname} -> ${roomId}`);
      Logger.error('WebSocket连接权限验证失败', error, 'CONNECTION', connectionId);
      
      const ws = uniCloud.webSocketServer();
      await ws.send(connectionId, JSON.stringify({
        action: 'connection_error',
        success: false,
        message: '无权限访问该房间',
        timestamp: Date.now()
      }));
      
      await ws.close(connectionId);
      Logger.connection('FAILED', connectionId, userInfo, roomId, {
        reason: 'access_denied',
        duration: Date.now() - startTime
      });
      return;
    }

    Logger.debug('房间权限验证通过', {
      roomId,
      userId: userInfo._id,
      nickname: userInfo.nickname,
      roomPlayers: room.players.length,
      roomStatus: room.room_status
    }, 'CONNECTION');

    // 将连接加入房间连接池
    const addResult = addConnectionToRoom(roomId, connectionId, userInfo._id, userInfo.nickname, userInfo.avatar_fileId);
    if (!addResult) {
      const error = new Error(`房间连接数已满: ${roomId}`);
      Logger.error('WebSocket连接池添加失败', error, 'CONNECTION', connectionId);
      
      const ws = uniCloud.webSocketServer();
      await ws.send(connectionId, JSON.stringify({
        action: 'connection_error',
        success: false,
        message: '房间连接数已满',
        timestamp: Date.now()
      }));
      
      await ws.close(connectionId);
      Logger.connection('FAILED', connectionId, userInfo, roomId, {
        reason: 'room_full',
        duration: Date.now() - startTime
      });
      return;
    }

    // 同步连接状态到数据库
    await syncConnectionToDatabase(connectionId, roomId, userInfo._id, userInfo.nickname, userInfo.avatar_fileId);

    const onlineCount = (await getRoomConnections(roomId)).size;
    const connectionDuration = Date.now() - startTime;
    
    Logger.connection('CONNECT', connectionId, userInfo, roomId, {
      onlineCount,
      duration: connectionDuration,
      roomStatus: room.room_status
    });
    
    // 发送连接成功消息给当前用户
    const ws = uniCloud.webSocketServer();
    await ws.send(connectionId, JSON.stringify({
      action: 'connection_success',
      success: true,
      data: {
        connectionId,
        roomId: roomId,
        userId: userInfo._id,
        nickname: userInfo.nickname,
        onlineCount
      },
      timestamp: Date.now()
    }));

    // 智能检测用户是否为新加入用户
    const isNewJoiner = checkIfNewJoiner(userInfo._id, roomId, room);
    
    if (isNewJoiner) {
      // 新用户加入流程：生成系统消息并广播完整信息
      Logger.info('检测到新用户加入，执行完整加入流程', {
        userId: userInfo._id,
        nickname: userInfo.nickname,
        roomId
      }, 'NEW_JOINER');
      
      await handleNewUserJoinNotification(roomId, userInfo, connectionId, onlineCount);
    } else {
      // 现有用户重新连接：仅广播上线通知
      Logger.debug('现有用户重新连接，广播上线通知', {
        userId: userInfo._id,
        nickname: userInfo.nickname,
        roomId
      }, 'RECONNECT');
      
      await broadcastToRoom(roomId, {
        action: 'player_online',
        data: {
          userId: userInfo._id,
          nickname: userInfo.nickname,
          avatar: userInfo.avatar_fileId,
          onlineCount
        }
      }, connectionId);
    }

    Logger.business('用户上线通知已广播', userInfo._id, roomId, {
      onlineCount,
      totalDuration: Date.now() - startTime,
      isNewJoiner
    }, connectionId);
    
  } catch (error) {
    const totalDuration = Date.now() - startTime;
    Logger.error('WebSocket连接处理异常', error, 'CONNECTION', connectionId);
    Logger.connection('ERROR', connectionId, null, null, {
      duration: totalDuration,
      errorType: error.name,
      errorMessage: error.message
    });
    
    // 发送错误消息并关闭连接
    try {
      const ws = uniCloud.webSocketServer();
      await ws.send(connectionId, JSON.stringify({
        action: 'connection_error',
        success: false,
        message: '连接处理异常',
        timestamp: Date.now()
      }));
      await ws.close(connectionId);
    } catch (closeError) {
      Logger.error('关闭异常连接失败', closeError, 'CONNECTION', connectionId);
    }
  }
};

// WebSocket消息事件
exports.onWebsocketMessage = async function (event, context) {
  const startTime = Date.now();
  const { connectionId, payload } = event;
  
  Logger.debug('收到WebSocket消息', {
    connectionId,
    payloadSize: payload?.length || 0,
    context: {
      requestId: context.requestId,
      source: context.source
    }
  }, 'MESSAGE');
  
  let requestId = null;
  let action = null;
  
  try {
    // 解析消息
    let message;
    try {
      message = JSON.parse(payload);
      action = message.action;
      requestId = message.requestId;
      
      Logger.debug('WebSocket消息解析成功', {
        connectionId,
        action,
        requestId,
        hasData: !!message.data,
        dataKeys: message.data ? Object.keys(message.data) : []
      }, 'MESSAGE', requestId);
      
    } catch (parseError) {
      Logger.error('WebSocket消息格式错误', parseError, 'MESSAGE', connectionId);
      await sendResponse(connectionId, 'parse_error', false, null, null, '消息格式错误');
      return;
    }

    const { data } = message;
    
    if (!action) {
      Logger.warn('WebSocket消息缺少action参数', {
        connectionId,
        requestId,
        messageKeys: Object.keys(message)
      }, 'MESSAGE', requestId);
      await sendResponse(connectionId, 'invalid_action', false, null, requestId, '缺少action参数');
      return;
    }

    // 开始请求跟踪
    Logger.startRequest(requestId || connectionId + '_' + Date.now(), action, {
      connectionId,
      action,
      dataSize: data ? JSON.stringify(data).length : 0
    });

    Logger.info(`开始处理WebSocket操作`, {
      action,
      requestId,
      connectionId,
      dataPresent: !!data
    }, 'MESSAGE', requestId);

    // 根据action分发到对应的处理函数
    const actionStartTime = Date.now();
    let actionResult = { success: false };
    
    try {
      switch (action) {
        case 'single_score':
          await handleSingleScore(connectionId, data, requestId);
          actionResult.success = true;
          break;
        case 'multiple_score':
          await handleMultipleScore(connectionId, data, requestId);
          actionResult.success = true;
          break;
        case 'distribute':
          await handleDistribute(connectionId, data, requestId);
          actionResult.success = true;
          break;
        case 'collect':
          await handleCollect(connectionId, data, requestId);
          actionResult.success = true;
          break;
        case 'tea_settings':
          await handleTeaSettings(connectionId, data, requestId);
          actionResult.success = true;
          break;
        case 'tea_score':
          await handleTeaScore(connectionId, data, requestId);
          actionResult.success = true;
          break;
        case 'player_join':
          await handlePlayerJoin(connectionId, data, requestId);
          actionResult.success = true;
          break;
        case 'settlement':
          await handleSettlement(connectionId, data, requestId);
          actionResult.success = true;
          break;
        case 'player_leave':
          await handlePlayerLeave(connectionId, data, requestId);
          actionResult.success = true;
          break;
        default:
          Logger.warn('不支持的操作类型', {
            action,
            requestId,
            connectionId
          }, 'MESSAGE', requestId);
          await sendResponse(connectionId, action, false, null, requestId, `不支持的操作类型: ${action}`);
          actionResult.success = false;
          actionResult.error = `Unsupported action: ${action}`;
          break;
      }
      
      const actionDuration = Date.now() - actionStartTime;
      Logger.performance(`handleAction_${action}`, actionDuration, {
        connectionId,
        requestId,
        success: actionResult.success
      }, requestId);
      
      // 结束请求跟踪
      Logger.endRequest(requestId || connectionId + '_' + Date.now(), actionResult.success, actionResult, actionResult.error);
      
    } catch (actionError) {
      const actionDuration = Date.now() - actionStartTime;
      Logger.error(`WebSocket操作处理失败: ${action}`, actionError, 'MESSAGE', requestId);
      Logger.performance(`handleAction_${action}`, actionDuration, { 
        success: false,
        connectionId,
        requestId
      }, requestId);
      
      // 结束请求跟踪
      Logger.endRequest(requestId || connectionId + '_' + Date.now(), false, null, actionError);
      throw actionError; // 重新抛出异常以便外层处理
    }
    
  } catch (error) {
    const totalDuration = Date.now() - startTime;
    Logger.error('WebSocket消息处理异常', error, 'MESSAGE', requestId);
    Logger.performance('onWebsocketMessage', totalDuration, { 
      success: false,
      connectionId,
      action,
      requestId
    }, requestId);
    
    await sendResponse(connectionId, 'system_error', false, null, requestId, '系统异常，请稍后重试');
  }
};

// WebSocket断开连接事件
exports.onWebsocketDisConnection = async function (event, context) {
  const startTime = Date.now();
  const { connectionId } = event;
  
  Logger.connection('DISCONNECT_ATTEMPT', connectionId, null, null, {
    context: {
      requestId: context.requestId,
      source: context.source
    }
  });
  
  try {
    // 查找并移除连接
    let removedConnection = null;
    let roomId = null;
    
    Logger.debug('开始查找连接记录', {
      connectionId,
      totalRooms: roomConnectionPools.size
    }, 'DISCONNECT');
    
    // 遍历所有房间查找该连接
    const searchStartTime = Date.now();
    for (const [currentRoomId, roomConnections] of roomConnectionPools) {
      for (const conn of roomConnections) {
        if (conn.connectionId === connectionId) {
          roomId = currentRoomId;
          removedConnection = removeConnectionFromRoom(roomId, connectionId);
          break;
        }
      }
      if (removedConnection) break;
    }
    
    const searchDuration = Date.now() - searchStartTime;
    Logger.performance('connectionLookup', searchDuration, {
      connectionId,
      found: !!removedConnection,
      roomId
    });
    
    if (removedConnection && roomId) {
      const connectionDuration = Date.now() - removedConnection.joinTime.getTime();
      
      Logger.connection('DISCONNECT', connectionId, {
        _id: removedConnection.userId,
        nickname: removedConnection.nickname
      }, roomId, {
        connectionDuration,
        lastHeartbeat: removedConnection.lastHeartbeat.toISOString()
      });
      
      // 从数据库移除连接记录
      const dbStartTime = Date.now();
      await removeConnectionFromDatabase(connectionId, 'normal');
      Logger.performance('removeConnectionFromDatabase', Date.now() - dbStartTime);
      
      // 向房间其他玩家广播用户离线通知
      const remainingConnections = await getRoomConnections(roomId);
      if (remainingConnections.size > 0) {
        const broadcastStartTime = Date.now();
        await broadcastToRoom(roomId, {
          action: 'player_offline',
          data: {
            userId: removedConnection.userId,
            nickname: removedConnection.nickname,
            reason: 'disconnected',
            onlineCount: remainingConnections.size
          }
        });
        
        Logger.performance('offlineBroadcast', Date.now() - broadcastStartTime);
        Logger.business('用户离线通知已广播', removedConnection.userId, roomId, {
          onlineCount: remainingConnections.size,
          connectionDuration,
          broadcastTargets: remainingConnections.size
        });
      } else {
        Logger.info('房间已无其他连接，跳过离线广播', {
          roomId,
          disconnectedUser: removedConnection.nickname
        }, 'DISCONNECT');
      }
      
    } else {
      Logger.warn('WebSocket连接断开，但未找到对应的连接记录', {
        connectionId,
        searchedRooms: roomConnectionPools.size,
        searchDuration
      }, 'DISCONNECT');
      
      // 尝试从数据库清理
      await removeConnectionFromDatabase(connectionId, 'normal');
    }
    
    const totalDuration = Date.now() - startTime;
    Logger.info('WebSocket断开连接处理完成', {
      connectionId,
      found: !!removedConnection,
      roomId,
      userNickname: removedConnection?.nickname,
      duration: totalDuration
    }, 'DISCONNECT');
    
    Logger.performance('onWebsocketDisConnection', totalDuration, {
      connectionId,
      found: !!removedConnection,
      roomId
    });
    
  } catch (error) {
    const totalDuration = Date.now() - startTime;
    Logger.error('WebSocket断开连接处理异常', error, 'DISCONNECT');
    Logger.performance('onWebsocketDisConnection', totalDuration, { 
      success: false,
      connectionId 
    });
    
    // 确保数据库记录被清理
    try {
      await removeConnectionFromDatabase(connectionId, 'error');
    } catch (dbError) {
      Logger.error('清理数据库连接记录失败', dbError, 'DISCONNECT');
    }
  }
};

// WebSocket错误事件
exports.onWebsocketError = async function (event, context) {
  const startTime = Date.now();
  const { connectionId, errorMessage } = event;
  
  Logger.connection('ERROR', connectionId, null, null, {
    errorMessage,
    context: {
      requestId: context.requestId,
      source: context.source
    }
  });
  
  try {
    Logger.error('WebSocket错误事件', new Error(errorMessage), 'WEBSOCKET_ERROR', connectionId);
    
    // 尝试清理可能的连接记录
    let roomId = null;
    for (const [currentRoomId, roomConnections] of roomConnectionPools) {
      for (const conn of roomConnections) {
        if (conn.connectionId === connectionId) {
          roomId = currentRoomId;
          break;
        }
      }
      if (roomId) break;
    }
    
    if (roomId) {
      Logger.warn('WebSocket错误导致的连接清理', {
        connectionId,
        roomId,
        errorMessage
      }, 'WEBSOCKET_ERROR');
      
      // 可能需要清理该连接
      removeConnectionFromRoom(roomId, connectionId);
      await removeConnectionFromDatabase(connectionId, 'error');
    }
    
    const duration = Date.now() - startTime;
    Logger.performance('onWebsocketError', duration, {
      connectionId,
      hadConnection: !!roomId
    });
    
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('WebSocket错误处理异常', error, 'WEBSOCKET_ERROR', connectionId);
    Logger.performance('onWebsocketError', duration, { success: false });
  }
};

// ==================== 业务处理函数 ====================

/**
 * 处理单人给分
 * @param {string} connectionId 连接ID
 * @param {Object} data 请求数据 { token, roomId, player, score }
 * @param {string} requestId 请求ID
 */
async function handleSingleScore(connectionId, data, requestId) {
  const startTime = Date.now();
  
  try {
    Logger.business('开始处理单人给分', null, data?.roomId, {
      connectionId,
      requestId,
      targetPlayer: data?.player?.name,
      score: data?.score
    }, requestId);
    
    const { token, roomId, player, score } = data;
    
    // 参数验证
    if (!token || !roomId || !player || typeof score !== 'number') {
      Logger.warn('单人给分参数验证失败', {
        hasToken: !!token,
        hasRoomId: !!roomId,
        hasPlayer: !!player,
        scoreType: typeof score,
        connectionId,
        requestId
      }, 'BUSINESS', requestId);
      await sendResponse(connectionId, 'single_score', false, null, requestId, '参数不完整');
      return;
    }

    Logger.debug('单人给分参数验证通过', {
      roomId,
      targetPlayerId: player.id,
      targetPlayerName: player.name,
      score,
      connectionId
    }, 'BUSINESS', requestId);

    // 初始化数据库连接
    const db = uniCloud.database();
    const roomsCollection = db.collection("rooms");
    const usersCollection = db.collection("users");
    const messagesCollection = db.collection("room_messages");

    // 验证用户token
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    if (!userVerifyResult.success) {
      await sendResponse(connectionId, 'single_score', false, null, requestId, userVerifyResult.message);
      return;
    }

    const { userInfo } = userVerifyResult.data;

    // 验证房间存在且用户有权限
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    if (roomRes.data.length === 0) {
      await sendResponse(connectionId, 'single_score', false, null, requestId, '房间不存在');
      return;
    }

    const room = roomRes.data[0];
    const userInRoom = room.players.some(p => p.user_id === userInfo._id && p.has_left !== true);
    if (!userInRoom) {
      await sendResponse(connectionId, 'single_score', false, null, requestId, '无权限访问该房间');
      return;
    }

    // 获取茶水设置
    const teaWaterRatio = room.tea_water_ratio || 0;
    const teaWaterLimitAmount = room.tea_water_limit_amount;
    const currentTeaBalance = room.tea_water_balance || 0;

    // 计算茶水抽佣
    const { teaAmount, actualAmount } = calculateTeaDeduction(score, teaWaterRatio, currentTeaBalance, teaWaterLimitAmount);

    // 构建消息数据
    const messageData = {
      room_id: roomId,
      message_type: "score",
      sender_id: userInfo._id,
      sender_name: userInfo.nickname,
      sender_avatar_fileId: userInfo.avatar_fileId,
      timestamp: new Date(),
      detail_data: {
        target_id: player.id,
        target_name: player.name,
        target_avatar_fileId: player.avatar_fileId || "",
        amount: actualAmount,
        original_amount: score,
        tea_amount: teaAmount
      }
    };

    // 使用事务处理消息添加和茶水余额更新
    const transaction = await db.startTransaction();
    
    try {
      // 添加消息记录
      const addResult = await transaction.collection("room_messages").add(messageData);
      
      // 更新茶水余额
      const newTeaBalance = NP.plus(currentTeaBalance, teaAmount);
      await transaction.collection("rooms").doc(room._id).update({
        tea_water_balance: newTeaBalance
      });

      // 更新房间状态（如果是第一次计分）
      if (room.room_status === "waiting") {
        const activePlayers = room.players.filter(player => player.has_left !== true);
        if (activePlayers.length >= 2) {
          await transaction.collection("rooms").doc(room._id).update({
            room_status: "playing",
            start_time: new Date()
          });
        }
      }

      // 提交事务
      await transaction.commit();

      // 标记用户参与房间（WebSocket路径需要手动调用）
      try {
        const userObj = uniCloud.importObject("user");
        
        // 标记发送者参与房间
        const markSenderResult = await userObj.markUserRoomParticipation(
          userInfo._id,
          roomId
        );
        
        if (markSenderResult.code !== 200) {
          console.warn(`WebSocket单人给分-标记发送者参与房间失败: ${markSenderResult.message}`);
        }

        // 标记接收者参与房间
        const markTargetResult = await userObj.markUserRoomParticipation(
          player.id,
          roomId
        );
        
        if (markTargetResult.code !== 200) {
          console.warn(`WebSocket单人给分-标记接收者参与房间失败: ${markTargetResult.message}`);
        }
      } catch (markError) {
        console.error("WebSocket单人给分-调用markUserRoomParticipation失败:", markError);
        // 不阻断主流程，只记录错误
      }

      // 准备增量更新数据
      const incrementalData = {
        players: [
          { id: userInfo._id, scoreChange: NP.times(score, -1) }, // 发送者减少原始分数
          { id: player.id, scoreChange: actualAmount } // 接收者增加实际分数
        ],
        teaWaterBalance: newTeaBalance,
        newMessage: {
          id: addResult.id,
          type: "score",
          timestamp: formatTimestamp(messageData.timestamp),
          sender: {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar_fileId: userInfo.avatar_fileId
          },
          target: {
            id: player.id,
            name: player.name,
            avatar_fileId: player.avatar_fileId
          },
          amount: actualAmount,
          original_amount: score,
          tea_amount: teaAmount,
          isSelf: true
        }
      };

      // 准备操作发起者的完整响应
      const operatorResponse = {
        action: 'single_score_result',
        success: true,
        data: { incremental: incrementalData },
        requestId,
        message: '给分成功',
        timestamp: Date.now()
      };

      // 准备房间广播数据（给其他玩家）
      const broadcastData = {
        operationType: 'single_score',
        operator: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar_fileId: userInfo.avatar_fileId
        },
        target: {
          id: player.id,
          name: player.name,
          avatar_fileId: player.avatar_fileId
        },
        scoreChange: {
          original: score,
          actual: actualAmount,
          tea: teaAmount
        },
        incremental: {
          players: incrementalData.players,
          teaWaterBalance: incrementalData.teaWaterBalance,
          newMessage: {
            ...incrementalData.newMessage,
            isSelf: false // 对其他玩家来说不是自己的操作
          }
        }
      };

      // 检查房间连接状态
      const roomConnections = await getRoomConnections(roomId);
      console.log(`房间 ${roomId} 当前连接数: ${roomConnections.size}`);
      
      // 广播操作结果
      await broadcastOperationResult(roomId, connectionId, operatorResponse, broadcastData);
      
      console.log(`单人给分处理成功并已广播 - 操作者: ${userInfo.nickname}, 目标: ${player.name}, 分数: ${actualAmount}`);
      
    } catch (transactionError) {
      await transaction.rollback();
      console.error('单人给分事务失败:', transactionError);
      await sendResponse(connectionId, 'single_score', false, null, requestId, '给分失败，请稍后重试');
    }
    
  } catch (error) {
    console.error('处理单人给分异常:', error);
    await sendResponse(connectionId, 'single_score', false, null, requestId, '处理单人给分失败');
  }
}

/**
 * 处理多人给分
 * @param {string} connectionId 连接ID
 * @param {Object} data 请求数据 { token, roomId, entries: [{ player, score }] }
 * @param {string} requestId 请求ID
 */
async function handleMultipleScore(connectionId, data, requestId) {
  try {
    console.log('处理多人给分:', data);
    
    const { token, roomId, entries } = data;
    
    if (!token || !roomId || !entries || !Array.isArray(entries) || entries.length === 0) {
      await sendResponse(connectionId, 'multiple_score', false, null, requestId, '参数不完整');
      return;
    }

    // 初始化数据库连接
    const db = uniCloud.database();
    const roomsCollection = db.collection("rooms");
    const usersCollection = db.collection("users");

    // 验证用户token
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    if (!userVerifyResult.success) {
      await sendResponse(connectionId, 'multiple_score', false, null, requestId, userVerifyResult.message);
      return;
    }

    const { userInfo } = userVerifyResult.data;

    // 验证房间存在且用户有权限
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    if (roomRes.data.length === 0) {
      await sendResponse(connectionId, 'multiple_score', false, null, requestId, '房间不存在');
      return;
    }

    const room = roomRes.data[0];
    const userInRoom = room.players.some(p => p.user_id === userInfo._id && p.has_left !== true);
    if (!userInRoom) {
      await sendResponse(connectionId, 'multiple_score', false, null, requestId, '无权限访问该房间');
      return;
    }

    // 获取茶水设置
    const teaWaterRatio = room.tea_water_ratio || 0;
    const teaWaterLimitAmount = room.tea_water_limit_amount;
    let currentTeaBalance = room.tea_water_balance || 0;

    // 准备批量处理的数据
    const messageEntries = [];
    const playerUpdates = [];
    let totalTeaAmount = 0;
    let successCount = 0;

    // 为每个条目计算茶水抽佣和准备数据
    for (const entry of entries) {
      const { player, score } = entry;
      
      if (!player || !player.id || typeof score !== 'number') {
        console.warn('跳过无效的给分条目:', entry);
        continue;
      }

      // 计算茶水抽佣（使用当前累计的茶水余额）
      const { teaAmount, actualAmount } = calculateTeaDeduction(score, teaWaterRatio, currentTeaBalance, teaWaterLimitAmount);
      
      // 更新临时茶水余额，用于下一个条目的计算
      currentTeaBalance = NP.plus(currentTeaBalance, teaAmount);
      totalTeaAmount = NP.plus(totalTeaAmount, teaAmount);

      // 准备消息数据
      messageEntries.push({
        room_id: roomId,
        message_type: "score",
        sender_id: userInfo._id,
        sender_name: userInfo.nickname,
        sender_avatar_fileId: userInfo.avatar_fileId,
        timestamp: new Date(),
        detail_data: {
          target_id: player.id,
          target_name: player.name,
          target_avatar_fileId: player.avatar_fileId || "",
          amount: actualAmount,
          original_amount: score,
          tea_amount: teaAmount
        }
      });

      // 准备玩家分数更新数据
      playerUpdates.push(
        { id: userInfo._id, scoreChange: NP.times(score, -1) }, // 发送者减少原始分数
        { id: player.id, scoreChange: actualAmount } // 接收者增加实际分数
      );

      successCount++;
    }

    if (successCount === 0) {
      await sendResponse(connectionId, 'multiple_score', false, null, requestId, '没有有效的给分条目');
      return;
    }

    // 使用事务处理批量消息添加和茶水余额更新
    const transaction = await db.startTransaction();
    
    try {
      // 批量添加消息记录
      const addResults = [];
      for (const messageData of messageEntries) {
        const addResult = await transaction.collection("room_messages").add(messageData);
        addResults.push({
          id: addResult.id,
          ...messageData
        });
      }
      
      // 准备房间更新数据
      const roomUpdateData = {
        tea_water_balance: NP.plus(room.tea_water_balance || 0, totalTeaAmount)
      };

      // 如果是第一次计分，同时更新房间状态
      if (room.room_status === "waiting") {
        const activePlayers = room.players.filter(player => player.has_left !== true);
        if (activePlayers.length >= 2) {
          roomUpdateData.room_status = "playing";
          roomUpdateData.start_time = new Date();
        }
      }

      // 一次性更新房间信息
      await transaction.collection("rooms").doc(room._id).update(roomUpdateData);

      // 提交事务
      await transaction.commit();

      // 标记用户参与房间（WebSocket路径需要手动调用）
      try {
        const userObj = uniCloud.importObject("user");
        
        // 标记发送者参与房间
        const markSenderResult = await userObj.markUserRoomParticipation(
          userInfo._id,
          roomId
        );
        
        if (markSenderResult.code !== 200) {
          console.warn(`WebSocket多人给分-标记发送者参与房间失败: ${markSenderResult.message}`);
        }

        // 批量标记所有接收者参与房间
        for (const entry of entries) {
          if (entry.player.id !== userInfo._id) { // 避免重复标记发送者
            try {
              const markTargetResult = await userObj.markUserRoomParticipation(
                entry.player.id,
                roomId
              );
              
              if (markTargetResult.code !== 200) {
                console.warn(`WebSocket多人给分-标记接收者(${entry.player.name})参与房间失败: ${markTargetResult.message}`);
              }
            } catch (targetMarkError) {
              console.error(`WebSocket多人给分-标记接收者(${entry.player.name})参与房间异常:`, targetMarkError);
            }
          }
        }
      } catch (markError) {
        console.error("WebSocket多人给分-调用markUserRoomParticipation失败:", markError);
        // 不阻断主流程，只记录错误
      }

      // 合并相同玩家的分数变化
      const consolidatedPlayerUpdates = consolidatePlayerUpdates(playerUpdates);

      // 准备增量更新数据
      const newTeaBalance = roomUpdateData.tea_water_balance;
      const incrementalData = {
        players: consolidatedPlayerUpdates,
        teaWaterBalance: newTeaBalance,
        newMessages: addResults.map(result => ({
          id: result.id,
          type: "score",
          timestamp: formatTimestamp(result.timestamp),
          sender: {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar_fileId: userInfo.avatar_fileId
          },
          target: {
            id: result.detail_data.target_id,
            name: result.detail_data.target_name,
            avatar_fileId: result.detail_data.target_avatar_fileId
          },
          amount: result.detail_data.amount,
          original_amount: result.detail_data.original_amount,
          tea_amount: result.detail_data.tea_amount,
          isSelf: true
        }))
      };

      // 准备操作发起者的完整响应
      const operatorResponse = {
        action: 'multiple_score_result',
        success: true,
        data: { incremental: incrementalData },
        requestId,
        message: `给${successCount}位玩家记分成功`,
        timestamp: Date.now()
      };

      // 准备房间广播数据（给其他玩家）
      const broadcastData = {
        operationType: 'multiple_score',
        operator: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar_fileId: userInfo.avatar_fileId
        },
        targets: entries.map(entry => ({
          id: entry.player.id,
          name: entry.player.name,
          scoreChange: {
            original: entry.score,
            actual: entry.actualAmount,
            tea: entry.teaAmount
          }
        })),
        incremental: {
          players: incrementalData.players,
          teaWaterBalance: incrementalData.teaWaterBalance,
          newMessages: incrementalData.newMessages.map(msg => ({
            ...msg,
            isSelf: false // 对其他玩家来说不是自己的操作
          }))
        }
      };

      // 广播操作结果
      await broadcastOperationResult(roomId, connectionId, operatorResponse, broadcastData);
      
      console.log(`多人给分处理成功并已广播 - 操作者: ${userInfo.nickname}, 成功数: ${successCount}, 总茶水: ${totalTeaAmount}`);
      
    } catch (error) {
      // 只有在事务还未提交时才尝试回滚
      if (!error.message?.includes('already committed')) {
        try {
          await transaction.rollback();
        } catch (rollbackError) {
          console.error('事务回滚失败:', rollbackError);
        }
      }
      
      console.error('多人给分事务失败:', error);
      await sendResponse(connectionId, 'multiple_score', false, null, requestId, '给分失败，请稍后重试');
      return;
    }
    
  } catch (error) {
    console.error('处理多人给分异常:', error);
    await sendResponse(connectionId, 'multiple_score', false, null, requestId, '处理多人给分失败');
  }
}

/**
 * 处理出分到桌面
 * @param {string} connectionId 连接ID
 * @param {Object} data 请求数据 { token, roomId, amount }
 * @param {string} requestId 请求ID
 */
async function handleDistribute(connectionId, data, requestId) {
  try {
    console.log('处理出分到桌面:', data);
    
    const { token, roomId, amount } = data;
    
    if (!token || !roomId || typeof amount !== 'number' || amount <= 0) {
      await sendResponse(connectionId, 'distribute', false, null, requestId, '参数不完整或出分金额无效');
      return;
    }

    // 初始化数据库连接
    const db = uniCloud.database();
    const roomsCollection = db.collection("rooms");
    const usersCollection = db.collection("users");

    // 验证用户token
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    if (!userVerifyResult.success) {
      await sendResponse(connectionId, 'distribute', false, null, requestId, userVerifyResult.message);
      return;
    }

    const { userInfo } = userVerifyResult.data;

    // 验证房间存在且用户有权限
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    if (roomRes.data.length === 0) {
      await sendResponse(connectionId, 'distribute', false, null, requestId, '房间不存在');
      return;
    }

    const room = roomRes.data[0];
    const userInRoom = room.players.some(p => p.user_id === userInfo._id && p.has_left !== true);
    if (!userInRoom) {
      await sendResponse(connectionId, 'distribute', false, null, requestId, '无权限访问该房间');
      return;
    }

    // 检查房间玩家数量，只有一个玩家时不能出分
    const activePlayers = room.players.filter(p => !p.has_left);
    if (activePlayers.length <= 1) {
      await sendResponse(connectionId, 'distribute', false, null, requestId, '房间只有一个玩家，无法出分');
      return;
    }

    // 获取当前局数和桌面分数
    const currentRound = room.game_rounds || 1;
    const currentTableScore = await getCurrentTableScore(roomId, currentRound);
    const newTableScore = NP.plus(currentTableScore, amount);

    // 构建消息数据
    const messageData = {
      room_id: roomId,
      message_type: "distribute",
      sender_id: userInfo._id,
      sender_name: userInfo.nickname,
      sender_avatar_fileId: userInfo.avatar_fileId,
      timestamp: new Date(),
      detail_data: {
        amount: amount,
        round_number: currentRound,
        table_score_before: currentTableScore,
        table_score_after: newTableScore
      }
    };

    // 使用事务处理消息添加
    const transaction = await db.startTransaction();
    
    try {
      // 添加消息记录
      const addResult = await transaction.collection("room_messages").add(messageData);

      // 准备房间状态更新数据
      let roomUpdateData = null;
      
      // 更新房间状态（如果是第一次操作）
      if (room.room_status === "waiting") {
        const activePlayers = room.players.filter(player => player.has_left !== true);
        if (activePlayers.length >= 2) {
          roomUpdateData = {
            room_status: "playing",
            start_time: new Date()
          };
        }
      }
      
      // 如果需要更新房间状态，执行更新操作
      if (roomUpdateData) {
        await transaction.collection("rooms").doc(room._id).update(roomUpdateData);
        console.log('房间状态已更新为playing');
      }

      // 提交事务
      await transaction.commit();

      // 标记用户参与房间（WebSocket路径需要手动调用）
      try {
        const userObj = uniCloud.importObject("user");
        
        // 标记发送者参与房间
        const markResult = await userObj.markUserRoomParticipation(
          userInfo._id,
          roomId
        );
        
        if (markResult.code !== 200) {
          console.warn(`WebSocket出分-标记用户参与房间失败: ${markResult.message}`);
        }
      } catch (markError) {
        console.error("WebSocket出分-调用markUserRoomParticipation失败:", markError);
        // 不阻断主流程，只记录错误
      }

      // 准备增量更新数据
      const incrementalData = {
        players: [
          { id: userInfo._id, scoreChange: NP.times(amount, -1) } // 发送者减少分数
        ],
        tableScore: newTableScore,
        newMessage: {
          id: addResult.id,
          type: "distribute",
          timestamp: formatTimestamp(messageData.timestamp),
          sender: {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar_fileId: userInfo.avatar_fileId
          },
          amount: amount,
          round_number: currentRound,
          table_score_before: currentTableScore,
          table_score_after: newTableScore,
          isSelf: true
        }
      };

      // 准备操作发起者的完整响应
      const operatorResponse = {
        action: 'distribute_result',
        success: true,
        data: { incremental: incrementalData },
        requestId,
        message: '出分成功',
        timestamp: Date.now()
      };

      // 准备房间广播数据（给其他玩家）
      const broadcastData = {
        operationType: 'distribute',
        operator: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar_fileId: userInfo.avatar_fileId
        },
        amount: amount,
        tableScoreChange: {
          before: currentTableScore,
          after: newTableScore,
          change: amount
        },
        round: currentRound,
        incremental: {
          players: incrementalData.players,
          tableScore: incrementalData.tableScore,
          newMessage: {
            ...incrementalData.newMessage,
            isSelf: false // 对其他玩家来说不是自己的操作
          }
        }
      };

      // 检查房间连接状态
      const roomConnections = await getRoomConnections(roomId);
      console.log(`房间 ${roomId} 当前连接数: ${roomConnections.size}`);
      
      // 广播操作结果
      await broadcastOperationResult(roomId, connectionId, operatorResponse, broadcastData);
      
      console.log(`出分到桌面处理成功并已广播 - 操作者: ${userInfo.nickname}, 金额: ${amount}, 桌面分数: ${newTableScore}`);
      
    } catch (transactionError) {
      await transaction.rollback();
      console.error('出分到桌面事务失败:', transactionError);
      await sendResponse(connectionId, 'distribute', false, null, requestId, '出分失败，请稍后重试');
    }
    
  } catch (error) {
    console.error('处理出分到桌面异常:', error);
    await sendResponse(connectionId, 'distribute', false, null, requestId, '处理出分到桌面失败');
  }
}

/**
 * 处理收分
 * @param {string} connectionId 连接ID
 * @param {Object} data 请求数据 { token, roomId, amount }
 * @param {string} requestId 请求ID
 */
async function handleCollect(connectionId, data, requestId) {
  try {
    console.log('处理收分:', data);
    
    const { token, roomId, amount } = data;
    
    if (!token || !roomId || typeof amount !== 'number' || amount <= 0) {
      await sendResponse(connectionId, 'collect', false, null, requestId, '参数不完整或收分金额无效');
      return;
    }

    // 初始化数据库连接
    const db = uniCloud.database();
    const roomsCollection = db.collection("rooms");
    const usersCollection = db.collection("users");

    // 验证用户token
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    if (!userVerifyResult.success) {
      await sendResponse(connectionId, 'collect', false, null, requestId, userVerifyResult.message);
      return;
    }

    const { userInfo } = userVerifyResult.data;

    // 验证房间存在且用户有权限
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    if (roomRes.data.length === 0) {
      await sendResponse(connectionId, 'collect', false, null, requestId, '房间不存在');
      return;
    }

    const room = roomRes.data[0];
    const userInRoom = room.players.some(p => p.user_id === userInfo._id && p.has_left !== true);
    if (!userInRoom) {
      await sendResponse(connectionId, 'collect', false, null, requestId, '无权限访问该房间');
      return;
    }

    // 检查房间玩家数量，只有一个玩家时不能收分
    const activePlayers = room.players.filter(p => !p.has_left);
    if (activePlayers.length <= 1) {
      await sendResponse(connectionId, 'collect', false, null, requestId, '房间只有一个玩家，无法收分');
      return;
    }

    // 获取当前局数和桌面分数
    const currentRound = room.game_rounds || 1;
    const currentTableScore = await getCurrentTableScore(roomId, currentRound);
    
    // 检查桌面分数是否足够
    if (currentTableScore < amount) {
      await sendResponse(connectionId, 'collect', false, null, requestId, `桌面分数不足，当前桌面分数：${currentTableScore}，收分金额：${amount}`);
      return;
    }

    // 获取茶水设置
    const teaWaterRatio = room.tea_water_ratio || 0;
    const teaWaterLimitAmount = room.tea_water_limit_amount;
    const currentTeaBalance = room.tea_water_balance || 0;

    // 计算茶水抽佣
    const { teaAmount, actualAmount } = calculateTeaDeduction(amount, teaWaterRatio, currentTeaBalance, teaWaterLimitAmount);
    const newTableScore = NP.minus(currentTableScore, amount);

    // 构建消息数据
    const messageData = {
      room_id: roomId,
      message_type: "collect",
      sender_id: userInfo._id,
      sender_name: userInfo.nickname,
      sender_avatar_fileId: userInfo.avatar_fileId,
      timestamp: new Date(),
      detail_data: {
        original_amount: amount,
        actual_amount: actualAmount,
        tea_amount: teaAmount,
        round_number: currentRound,
        table_score_before: currentTableScore,
        table_score_after: newTableScore
      }
    };

    // 使用事务处理消息添加和茶水余额更新
    const transaction = await db.startTransaction();
    
    try {
      // 添加消息记录
      const addResult = await transaction.collection("room_messages").add(messageData);
      
      // 更新茶水余额
      const newTeaBalance = NP.plus(currentTeaBalance, teaAmount);
      let roomUpdateData = {
        tea_water_balance: newTeaBalance
      };

      // 更新房间状态（如果是第一次操作）
      if (room.room_status === "waiting") {
        const activePlayers = room.players.filter(player => player.has_left !== true);
        if (activePlayers.length >= 2) {
          roomUpdateData.room_status = "playing";
          roomUpdateData.start_time = new Date();
        }
      }
      
      // 执行房间数据更新
      await transaction.collection("rooms").doc(room._id).update(roomUpdateData);

      // 提交事务
      await transaction.commit();

      // 标记用户参与房间（WebSocket路径需要手动调用）
      try {
        const userObj = uniCloud.importObject("user");
        
        // 标记收分者参与房间
        const markResult = await userObj.markUserRoomParticipation(
          userInfo._id,
          roomId
        );
        
        if (markResult.code !== 200) {
          console.warn(`WebSocket收分-标记用户参与房间失败: ${markResult.message}`);
        }
      } catch (markError) {
        console.error("WebSocket收分-调用markUserRoomParticipation失败:", markError);
        // 不阻断主流程，只记录错误
      }

      // 检查是否需要自动进入下一局（桌面分数为0）
      const shouldTriggerNextRound = newTableScore === 0;
      let roundMessages = [];
      
      if (shouldTriggerNextRound) {
        try {
          // 计算下一局的局数
          const nextRound = currentRound + 1;
          
          // 发送局结束消息
          const endMessage = await db.collection("room_messages").add({
            room_id: roomId,
            message_type: "round_end",
            sender_id: userInfo._id,
            sender_name: userInfo.nickname,
            sender_avatar_fileId: userInfo.avatar_fileId,
            timestamp: new Date(),
            detail_data: {
              round_number: currentRound,
              message_text: `第${currentRound}局结束`,
              trigger_reason: "table_score_zero",
              final_table_score: 0
            }
          });

          // 发送新局开始消息
          const startMessage = await db.collection("room_messages").add({
            room_id: roomId,
            message_type: "round_start",
            sender_id: userInfo._id,
            sender_name: userInfo.nickname,
            sender_avatar_fileId: userInfo.avatar_fileId,
            timestamp: new Date(),
            detail_data: {
              round_number: nextRound,
              message_text: `第${nextRound}局开始`,
              trigger_reason: "auto"
            }
          });

          // 🔧 关键修复：更新房间的当前局数
          await db.collection("rooms").doc(room._id).update({
            game_rounds: nextRound
          });
          console.log(`房间局数已更新为第${nextRound}局`);

          roundMessages = [
            {
              id: endMessage.id,
              type: "round_end",
              timestamp: formatTimestamp(new Date()),
              sender: {
                id: userInfo._id,
                name: userInfo.nickname,
                avatar: userInfo.avatar_fileId
              },
              round_number: currentRound,
              message: `第${currentRound}局结束`,
              trigger_reason: "table_score_zero",
              final_table_score: 0,
              isSelf: true
            },
            {
              id: startMessage.id,
              type: "round_start",
              timestamp: formatTimestamp(new Date()),
              sender: {
                id: userInfo._id,
                name: userInfo.nickname,
                avatar_fileId: userInfo.avatar_fileId
              },
              round_number: nextRound,
              message: `第${nextRound}局开始`,
              trigger_reason: "auto",
              isSelf: true
            }
          ];
        } catch (roundError) {
          console.error('创建新局消息失败:', roundError);
          // 不影响主要的收分逻辑
        }
      }

      // 准备增量更新数据
      const incrementalData = {
        players: [
          { id: userInfo._id, scoreChange: actualAmount } // 收分者增加实际分数
        ],
        tableScore: newTableScore,
        teaWaterBalance: newTeaBalance,
        newMessage: {
          id: addResult.id,
          type: "collect",
          timestamp: formatTimestamp(messageData.timestamp),
          sender: {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar_fileId: userInfo.avatar_fileId
          },
          original_amount: amount,
          actual_amount: actualAmount,
          tea_amount: teaAmount,
          round_number: currentRound,
          table_score_before: currentTableScore,
          table_score_after: newTableScore,
          isSelf: true
        }
      };

      // 如果有新局消息，添加到增量更新中
      if (roundMessages.length > 0) {
        incrementalData.roundMessages = roundMessages;
      }

      // 准备操作发起者的完整响应
      const operatorResponse = {
        action: 'collect_result',
        success: true,
        data: { incremental: incrementalData },
        requestId,
        message: '收分成功',
        timestamp: Date.now()
      };

      // 准备房间广播数据（给其他玩家）
      const broadcastData = {
        operationType: 'collect',
        operator: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar_fileId: userInfo.avatar_fileId
        },
        amount: {
          original: amount,
          actual: actualAmount,
          tea: teaAmount
        },
        tableScoreChange: {
          before: currentTableScore,
          after: newTableScore,
          change: -amount
        },
        round: currentRound,
        nextRoundTriggered: shouldTriggerNextRound,
        incremental: {
          players: incrementalData.players,
          tableScore: incrementalData.tableScore,
          teaWaterBalance: incrementalData.teaWaterBalance,
          newMessage: {
            ...incrementalData.newMessage,
            isSelf: false // 对其他玩家来说不是自己的操作
          }
        }
      };

      // 如果有新局消息，添加到广播数据中
      if (incrementalData.roundMessages) {
        broadcastData.incremental.roundMessages = incrementalData.roundMessages.map(msg => ({
          ...msg,
          isSelf: false // 对其他玩家来说不是自己触发的
        }));
      }

      // 广播操作结果
      await broadcastOperationResult(roomId, connectionId, operatorResponse, broadcastData);
      
      console.log(`收分处理成功并已广播 - 操作者: ${userInfo.nickname}, 金额: ${actualAmount}, 桌面分数: ${newTableScore}${shouldTriggerNextRound ? ', 触发下一局' : ''}`);
      
    } catch (transactionError) {
      await transaction.rollback();
      console.error('收分事务失败:', transactionError);
      await sendResponse(connectionId, 'collect', false, null, requestId, '收分失败，请稍后重试');
    }
    
  } catch (error) {
    console.error('处理收分异常:', error);
    await sendResponse(connectionId, 'collect', false, null, requestId, '处理收分失败');
  }
}

/**
 * 处理茶水设置
 * @param {string} connectionId 连接ID
 * @param {Object} data 请求数据 { token, roomId, teaSettings: { teaWaterLimitAmount, teaWaterRatio } }
 * @param {string} requestId 请求ID
 */
async function handleTeaSettings(connectionId, data, requestId) {
  try {
    console.log('处理茶水设置:', data);
    
    const { token, roomId, teaSettings } = data;
    
    if (!token || !roomId || !teaSettings) {
      await sendResponse(connectionId, 'tea_settings', false, null, requestId, '参数不完整');
      return;
    }

    const { teaWaterLimitAmount, teaWaterRatio } = teaSettings;

    if (typeof teaWaterRatio !== 'number' || teaWaterRatio < 0 || teaWaterRatio > 10) {
      await sendResponse(connectionId, 'tea_settings', false, null, requestId, '茶水比例必须在0%-10%之间');
      return;
    }

    // 初始化数据库连接
    const db = uniCloud.database();
    const roomsCollection = db.collection("rooms");
    const usersCollection = db.collection("users");

    // 验证用户token
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    if (!userVerifyResult.success) {
      await sendResponse(connectionId, 'tea_settings', false, null, requestId, userVerifyResult.message);
      return;
    }

    const { userInfo } = userVerifyResult.data;

    // 验证房间存在且用户有权限
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    if (roomRes.data.length === 0) {
      await sendResponse(connectionId, 'tea_settings', false, null, requestId, '房间不存在');
      return;
    }

    const room = roomRes.data[0];
    const userInRoom = room.players.some(p => p.user_id === userInfo._id && p.has_left !== true);
    if (!userInRoom) {
      await sendResponse(connectionId, 'tea_settings', false, null, requestId, '无权限访问该房间');
      return;
    }

    // 生成茶水设置描述文本
    const limitText = teaWaterLimitAmount && teaWaterLimitAmount > 0 ? 
      `${teaWaterLimitAmount}分` : '无上限';

    // 构建消息数据
    const messageData = {
      room_id: roomId,
      message_type: "system",
      sender_id: userInfo._id,
      sender_name: userInfo.nickname,
      sender_avatar_fileId: userInfo.avatar_fileId,
      timestamp: new Date(),
      detail_data: {
        message_text: `修改了茶水设置，抽取比例：${teaWaterRatio}%，抽取上限：${limitText}`,
        action_type: "tea_settings"
      }
    };

    // 使用事务处理茶水设置更新和消息添加
    const transaction = await db.startTransaction();
    
    try {
      // 更新房间茶水设置
      await transaction.collection("rooms").doc(room._id).update({
        tea_water_limit_amount: teaWaterLimitAmount,
        tea_water_ratio: teaWaterRatio
      });

      // 添加系统消息记录
      const addResult = await transaction.collection("room_messages").add(messageData);

      // 提交事务
      await transaction.commit();

      // 准备增量更新数据
      const incrementalData = {
        teaWaterSettings: {
          teaWaterLimitAmount: teaWaterLimitAmount,
          teaWaterRatio: teaWaterRatio
        },
        newMessage: {
          id: addResult.id,
          type: "system",
          timestamp: formatTimestamp(messageData.timestamp),
          sender: {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar: userInfo.avatar_fileId
          },
          message: messageData.detail_data.message_text,
          actionType: messageData.detail_data.action_type,
          isSelf: true
        }
      };

      // 准备操作发起者的完整响应
      const operatorResponse = {
        action: 'tea_settings_result',
        success: true,
        data: { incremental: incrementalData },
        requestId,
        message: '茶水设置已保存',
        timestamp: Date.now()
      };

      // 准备房间广播数据（给其他玩家）
      const broadcastData = {
        operationType: 'tea_settings',
        operator: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar: userInfo.avatar_fileId
        },
        settings: {
          teaWaterLimitAmount: teaWaterLimitAmount,
          teaWaterRatio: teaWaterRatio,
          limitText: teaWaterLimitAmount && teaWaterLimitAmount > 0 ? `${teaWaterLimitAmount}分` : '无上限'
        },
        incremental: {
          teaWaterSettings: incrementalData.teaWaterSettings,
          newMessage: {
            ...incrementalData.newMessage,
            isSelf: false // 对其他玩家来说不是自己的操作
          }
        }
      };

      // 广播操作结果
      await broadcastOperationResult(roomId, connectionId, operatorResponse, broadcastData);
      
      console.log(`茶水设置处理成功并已广播 - 操作者: ${userInfo.nickname}, 比例: ${teaWaterRatio}%, 上限: ${teaWaterLimitAmount || '无'}`);
      
    } catch (transactionError) {
      await transaction.rollback();
      console.error('茶水设置事务失败:', transactionError);
      await sendResponse(connectionId, 'tea_settings', false, null, requestId, '茶水设置保存失败，请稍后重试');
    }
    
  } catch (error) {
    console.error('处理茶水设置异常:', error);
    await sendResponse(connectionId, 'tea_settings', false, null, requestId, '处理茶水设置失败');
  }
}

/**
 * 处理茶水计分
 * @param {string} connectionId 连接ID
 * @param {Object} data 请求数据 { token, roomId, score }
 * @param {string} requestId 请求ID
 */
async function handleTeaScore(connectionId, data, requestId) {
  try {
    console.log('处理茶水计分:', data);

    const { token, roomId, score } = data;

    if (!token || !roomId || typeof score !== 'number') {
      await sendResponse(connectionId, 'tea_score', false, null, requestId, '参数不完整');
      return;
    }

    // 初始化数据库连接
    const db = uniCloud.database();
    const roomsCollection = db.collection("rooms");
    const usersCollection = db.collection("users");

    // 验证用户token
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    if (!userVerifyResult.success) {
      await sendResponse(connectionId, 'tea_score', false, null, requestId, userVerifyResult.message);
      return;
    }

    const { userInfo } = userVerifyResult.data;

    // 验证房间存在且用户有权限
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    if (roomRes.data.length === 0) {
      await sendResponse(connectionId, 'tea_score', false, null, requestId, '房间不存在');
      return;
    }

    const room = roomRes.data[0];
    const userInRoom = room.players.some(p => p.user_id === userInfo._id && p.has_left !== true);
    if (!userInRoom) {
      await sendResponse(connectionId, 'tea_score', false, null, requestId, '无权限访问该房间');
      return;
    }

    // 构建茶水计分消息数据
    const messageData = {
      room_id: roomId,
      message_type: "score",
      sender_id: userInfo._id,
      sender_name: userInfo.nickname,
      sender_avatar_fileId: userInfo.avatar_fileId,
      timestamp: new Date(),
      detail_data: {
        target_id: "tea_water",
        target_name: "茶水",
        target_avatar_fileId: "",
        amount: score,
        original_amount: score,
        tea_amount: 0 // 茶水计分不抽取茶水
      }
    };

    // 使用事务处理消息添加和茶水余额更新
    const transaction = await db.startTransaction();

    try {
      // 添加消息记录
      const addResult = await transaction.collection("room_messages").add(messageData);

      // 更新茶水余额
      const newTeaBalance = NP.plus(room.tea_water_balance || 0, score);
      await transaction.collection("rooms").doc(room._id).update({
        tea_water_balance: newTeaBalance
      });

      await transaction.commit();

      // 格式化消息数据为前端期望的格式
      const formattedMessage = {
        id: addResult.id,
        type: messageData.message_type,
        timestamp: formatTimestamp(messageData.timestamp),
        sender: {
          id: messageData.sender_id,
          name: messageData.sender_name,
          avatar_fileId: messageData.sender_avatar_fileId,
        },
        target: {
          id: messageData.detail_data.target_id,
          name: messageData.detail_data.target_name,
          avatar_fileId: messageData.detail_data.target_avatar_fileId || "",
        },
        amount: messageData.detail_data.amount,
        original_amount: messageData.detail_data.original_amount || messageData.detail_data.amount,
        tea_amount: messageData.detail_data.tea_amount || 0,
        isSelf: false // 这个字段会在前端根据当前用户ID重新计算
      };

      // 准备增量更新数据
      const incrementalData = {
        players: [
          { id: userInfo._id, scoreChange: NP.times(score, -1) } // 出分人减少分数
        ],
        teaWaterBalance: newTeaBalance,
        newMessage: formattedMessage
      };

      // 构建响应数据
      const operatorResponse = {
        success: true,
        data: {
          incremental: incrementalData
        }
      };

      // 构建广播数据
      const broadcastData = {
        operationType: 'tea_score',
        operator: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar: userInfo.avatar_fileId
        },
        teaScore: score,
        newTeaBalance: newTeaBalance,
        incremental: incrementalData
      };

      // 发送响应给操作者
      await sendResponse(connectionId, 'tea_score', true, operatorResponse.data, requestId);

      // 广播操作结果
      await broadcastOperationResult(roomId, connectionId, operatorResponse, broadcastData);

      console.log(`茶水计分处理成功并已广播 - 操作者: ${userInfo.nickname}, 分数: ${score}, 新余额: ${newTeaBalance}`);

    } catch (transactionError) {
      await transaction.rollback();
      console.error('茶水计分事务失败:', transactionError);
      await sendResponse(connectionId, 'tea_score', false, null, requestId, '茶水计分失败，请稍后重试');
    }

  } catch (error) {
    console.error('处理茶水计分异常:', error);
    await sendResponse(connectionId, 'tea_score', false, null, requestId, '处理茶水计分失败');
  }
}

/**
 * 处理玩家加入
 * @param {string} connectionId 连接ID
 * @param {Object} data 请求数据 { token, roomId, joinCode }
 * @param {string} requestId 请求ID
 */
async function handlePlayerJoin(connectionId, data, requestId) {
  try {
    console.log('处理玩家加入:', data);
    
    const { token, roomId, joinCode } = data;
    
    if (!token || !roomId || !joinCode) {
      await sendResponse(connectionId, 'player_join', false, null, requestId, '参数不完整');
      return;
    }

    // 初始化数据库连接
    const db = uniCloud.database();
    const roomsCollection = db.collection("rooms");
    const usersCollection = db.collection("users");

    // 验证用户token
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    if (!userVerifyResult.success) {
      await sendResponse(connectionId, 'player_join', false, null, requestId, userVerifyResult.message);
      return;
    }

    const { userInfo } = userVerifyResult.data;

    // 验证房间存在并获取房间信息
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    if (roomRes.data.length === 0) {
      await sendResponse(connectionId, 'player_join', false, null, requestId, '房间不存在');
      return;
    }

    const room = roomRes.data[0];

    // 验证邀请码
    if (room.join_code !== joinCode) {
      await sendResponse(connectionId, 'player_join', false, null, requestId, '邀请码错误');
      return;
    }

    // 检查玩家是否已在房间中
    const existingPlayer = room.players.find(p => p.user_id === userInfo._id);
    if (existingPlayer && !existingPlayer.has_left) {
      await sendResponse(connectionId, 'player_join', false, null, requestId, '您已在房间中');
      return;
    }

    // 检查房间人数限制
    const activePlayers = room.players.filter(p => !p.has_left);
    if (activePlayers.length >= 8) { // 房间最大人数限制为8
      await sendResponse(connectionId, 'player_join', false, null, requestId, '房间人数已满');
      return;
    }

    // 构建新玩家数据
    const newPlayer = {
      user_id: userInfo._id,
      nickname: userInfo.nickname,
      avatar_fileId: userInfo.avatar_fileId,
      score: 0,
      join_time: new Date(),
      has_left: false
    };

    // 构建系统消息数据
    const messageData = {
      room_id: roomId,
      message_type: "system",
      sender_id: userInfo._id,
      sender_name: userInfo.nickname,
      sender_avatar_fileId: userInfo.avatar_fileId,
      timestamp: new Date(),
      detail_data: {
        message_text: `${userInfo.nickname} 加入了房间`,
        action_type: "player_join"
      }
    };

    // 使用事务处理玩家加入和消息添加
    const transaction = await db.startTransaction();
    
    try {
      let updateData;
      
      if (existingPlayer && existingPlayer.has_left) {
        // 玩家重新加入，更新现有记录
        const playerIndex = room.players.findIndex(p => p.user_id === userInfo._id);
        room.players[playerIndex] = {
          ...existingPlayer,
          nickname: userInfo.nickname, // 更新昵称（可能已变更）
          avatar_fileId: userInfo.avatar_fileId, // 更新头像
          join_time: new Date(),
          has_left: false
        };
        updateData = { players: room.players };
      } else {
        // 新玩家加入
        updateData = {
          players: db.command.push(newPlayer)
        };
      }

      // 更新房间玩家列表
      await transaction.collection("rooms").doc(room._id).update(updateData);

      // 添加系统消息记录
      const addResult = await transaction.collection("room_messages").add(messageData);

      // 提交事务
      await transaction.commit();

      // 准备增量更新数据
      const incrementalData = {
        newPlayer: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar: userInfo.avatar_fileId,
          score: 0,
          isNewJoin: !existingPlayer || existingPlayer.has_left
        },
        newMessage: {
          id: addResult.id,
          type: "system",
          timestamp: formatTimestamp(messageData.timestamp),
          sender: {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar: userInfo.avatar_fileId
          },
          message: messageData.detail_data.message_text,
          actionType: messageData.detail_data.action_type,
          isSelf: true
        }
      };

      // 准备操作发起者的完整响应
      const operatorResponse = {
        action: 'player_join_result',
        success: true,
        data: { incremental: incrementalData },
        requestId,
        message: '加入房间成功',
        timestamp: Date.now()
      };

      // 准备房间广播数据（给其他玩家）
      const broadcastData = {
        operationType: 'player_join',
        operator: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar: userInfo.avatar_fileId
        },
        newPlayer: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar: userInfo.avatar_fileId,
          isReturning: existingPlayer && existingPlayer.has_left
        },
        incremental: {
          newPlayer: incrementalData.newPlayer,
          newMessage: {
            ...incrementalData.newMessage,
            isSelf: false // 对其他玩家来说不是自己的操作
          }
        }
      };

      // 广播操作结果
      await broadcastOperationResult(roomId, connectionId, operatorResponse, broadcastData);
      
      console.log(`玩家加入处理成功并已广播 - 玩家: ${userInfo.nickname}, 类型: ${existingPlayer && existingPlayer.has_left ? '重新加入' : '新加入'}`);
      
    } catch (transactionError) {
      await transaction.rollback();
      console.error('玩家加入事务失败:', transactionError);
      await sendResponse(connectionId, 'player_join', false, null, requestId, '加入房间失败，请稍后重试');
    }
    
  } catch (error) {
    console.error('处理玩家加入异常:', error);
    await sendResponse(connectionId, 'player_join', false, null, requestId, '处理玩家加入失败');
  }
}

/**
 * 处理结算单生成
 * @param {string} connectionId 连接ID
 * @param {Object} data 请求数据 { token, roomId, settlementData }
 * @param {string} requestId 请求ID
 */
async function handleSettlement(connectionId, data, requestId) {
  const startTime = Date.now();
  
  try {
    Logger.business('开始处理结算单生成', null, data?.roomId, {
      connectionId,
      requestId,
      hasSettlementData: !!data?.settlementData
    }, requestId);
    
    const { token, roomId, settlementData } = data;
    
    // 参数验证
    if (!token || !roomId || !settlementData) {
      Logger.warn('结算单生成参数验证失败', {
        hasToken: !!token,
        hasRoomId: !!roomId,
        hasSettlementData: !!settlementData,
        connectionId,
        requestId
      }, 'BUSINESS', requestId);
      await sendResponse(connectionId, 'settlement', false, null, requestId, '参数不完整');
      return;
    }

    const { details, totalLabel, totalAmount, scoreAdjustments, newTeaBalance } = settlementData;

    if (!details || !Array.isArray(details) || details.length === 0) {
      await sendResponse(connectionId, 'settlement', false, null, requestId, '结算数据不完整');
      return;
    }

    Logger.debug('结算单生成参数验证通过', {
      roomId,
      detailsCount: details.length,
      totalAmount,
      connectionId
    }, 'BUSINESS', requestId);

    // 初始化数据库连接
    const db = uniCloud.database();
    const roomsCollection = db.collection("rooms");
    const usersCollection = db.collection("users");
    const messagesCollection = db.collection("room_messages");

    // 验证用户token
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    if (!userVerifyResult.success) {
      await sendResponse(connectionId, 'settlement', false, null, requestId, userVerifyResult.message);
      return;
    }

    const { userInfo } = userVerifyResult.data;

    // 验证房间存在且用户有权限
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    if (roomRes.data.length === 0) {
      await sendResponse(connectionId, 'settlement', false, null, requestId, '房间不存在');
      return;
    }

    const room = roomRes.data[0];
    const userInRoom = room.players.some(p => p.user_id === userInfo._id && p.has_left !== true);
    if (!userInRoom) {
      await sendResponse(connectionId, 'settlement', false, null, requestId, '无权限访问该房间');
      return;
    }

    // 构建消息数据
    const messageData = {
      room_id: roomId,
      message_type: "settlement",
      sender_id: userInfo._id,
      sender_name: userInfo.nickname,
      sender_avatar_fileId: userInfo.avatar_fileId,
      timestamp: new Date(),
      detail_data: {
        details: details,
        total_label: totalLabel,
        total_amount: totalAmount,
        // 添加分数调整信息，用于分数计算
        score_adjustments: scoreAdjustments,
        new_tea_balance: newTeaBalance
      }
    };

    // 使用事务处理消息添加和相关更新
    const transaction = await db.startTransaction();
    
    try {
      // 添加结算消息记录
      const addResult = await transaction.collection("room_messages").add(messageData);

      // 更新茶水余额（如果有变化）
      if (newTeaBalance !== undefined && newTeaBalance !== (room.tea_water_balance || 0)) {
        await transaction.collection("rooms").doc(room._id).update({
          tea_water_balance: newTeaBalance
        });
      }

      // 提交事务
      await transaction.commit();

      // 标记相关玩家参与房间（WebSocket路径需要手动调用）
      try {
        const userObj = uniCloud.importObject("user");
        
        // 标记发起者参与房间
        const markSenderResult = await userObj.markUserRoomParticipation(
          userInfo._id,
          roomId
        );
        
        if (markSenderResult.code !== 200) {
          console.warn(`WebSocket结算单-标记发起者参与房间失败: ${markSenderResult.message}`);
        }

        // 标记所有涉及的玩家参与房间
        const involvedPlayerNames = new Set();
        details.forEach(detail => {
          if (detail.from !== '茶水') involvedPlayerNames.add(detail.from);
          if (detail.to !== '茶水') involvedPlayerNames.add(detail.to);
        });

        // 通过玩家名称找到对应的ID并标记参与
        for (const playerName of involvedPlayerNames) {
          const player = room.players.find(p => p.nickname === playerName);
          if (player && player.user_id !== userInfo._id) {
            try {
              const markResult = await userObj.markUserRoomParticipation(
                player.user_id,
                roomId
              );
              
              if (markResult.code !== 200) {
                console.warn(`WebSocket结算单-标记玩家(${playerName})参与房间失败: ${markResult.message}`);
              }
            } catch (playerMarkError) {
              console.error(`WebSocket结算单-标记玩家(${playerName})参与房间异常:`, playerMarkError);
            }
          }
        }
      } catch (markError) {
        console.error("WebSocket结算单-调用markUserRoomParticipation失败:", markError);
        // 不阻断主流程，只记录错误
      }

      // 计算分数调整（如果有）
      const playerUpdates = [];
      if (scoreAdjustments && Array.isArray(scoreAdjustments)) {
        scoreAdjustments.forEach(adjustment => {
          if (adjustment.playerId && typeof adjustment.adjustment === 'number') {
            playerUpdates.push({
              id: adjustment.playerId,
              scoreChange: adjustment.adjustment
            });
          }
        });
      }

      // 准备增量更新数据
      const incrementalData = {
        newMessage: {
          id: addResult.id,
          type: "settlement",
          timestamp: formatTimestamp(messageData.timestamp),
          sender: {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar_fileId: userInfo.avatar_fileId
          },
          creator: {
            name: userInfo.nickname,
            isSelf: true
          },
          details: details,
          totalLabel: totalLabel,
          totalAmount: totalAmount,
          // 添加分数调整信息，用于分数计算
          score_adjustments: scoreAdjustments,
          new_tea_balance: newTeaBalance,
          isSelf: true
        }
      };

      // 如果有分数调整，添加到增量数据中
      if (playerUpdates.length > 0) {
        incrementalData.players = playerUpdates;
      }

      // 如果茶水余额有变化，添加到增量数据中
      if (newTeaBalance !== undefined && newTeaBalance !== (room.tea_water_balance || 0)) {
        incrementalData.teaWaterBalance = newTeaBalance;
      }

      // 准备操作发起者的完整响应
      const operatorResponse = {
        action: 'settlement_result',
        success: true,
        data: { incremental: incrementalData },
        requestId,
        message: '结算单生成成功',
        timestamp: Date.now()
      };

      // 准备房间广播数据（给其他玩家）
      const broadcastData = {
        operationType: 'settlement',
        operator: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar_fileId: userInfo.avatar_fileId
        },
        settlement: {
          details: details,
          totalLabel: totalLabel,
          totalAmount: totalAmount,
          adjustmentsCount: playerUpdates.length
        },
        incremental: {
          newMessage: {
            ...incrementalData.newMessage,
            creator: {
              name: userInfo.nickname,
              isSelf: false // 对其他玩家来说不是自己生成的
            },
            isSelf: false // 对其他玩家来说不是自己的操作
          }
        }
      };

      // 如果有分数调整，添加到广播数据中
      if (incrementalData.players) {
        broadcastData.incremental.players = incrementalData.players;
      }

      // 如果茶水余额有变化，添加到广播数据中
      if (incrementalData.teaWaterBalance !== undefined) {
        broadcastData.incremental.teaWaterBalance = incrementalData.teaWaterBalance;
      }

      // 检查房间连接状态
      const roomConnections = await getRoomConnections(roomId);
      console.log(`房间 ${roomId} 当前连接数: ${roomConnections.size}`);
      
      // 广播操作结果
      await broadcastOperationResult(roomId, connectionId, operatorResponse, broadcastData);
      
      console.log(`结算单生成处理成功并已广播 - 操作者: ${userInfo.nickname}, 详情数: ${details.length}, 总金额: ${totalAmount}`);
      
    } catch (transactionError) {
      await transaction.rollback();
      console.error('结算单生成事务失败:', transactionError);
      await sendResponse(connectionId, 'settlement', false, null, requestId, '结算单生成失败，请稍后重试');
    }
    
  } catch (error) {
    console.error('处理结算单生成异常:', error);
    await sendResponse(connectionId, 'settlement', false, null, requestId, '处理结算单生成失败');
  }
}

/**
 * 处理玩家退出房间
 * @param {string} connectionId 连接ID
 * @param {Object} data 请求数据 { token, roomId }
 * @param {string} requestId 请求ID
 */
async function handlePlayerLeave(connectionId, data, requestId) {
  const startTime = Date.now();
  
  try {
    Logger.business('开始处理玩家退出房间', null, data?.roomId, {
      connectionId,
      requestId
    }, requestId);
    
    const { token, roomId } = data;
    
    if (!token || !roomId) {
      await sendResponse(connectionId, 'player_leave', false, null, requestId, '参数不完整');
      return;
    }

    Logger.debug('玩家退出房间参数验证通过', {
      roomId,
      connectionId
    }, 'BUSINESS', requestId);

    // 初始化数据库连接
    const db = uniCloud.database();
    const roomsCollection = db.collection("rooms");
    const usersCollection = db.collection("users");
    const messagesCollection = db.collection("room_messages");

    // 验证用户token
    const userVerifyResult = await verifyTokenAndGetUser(token, usersCollection);
    if (!userVerifyResult.success) {
      await sendResponse(connectionId, 'player_leave', false, null, requestId, userVerifyResult.message);
      return;
    }

    const { userInfo } = userVerifyResult.data;

    // 验证房间存在且用户有权限
    const roomRes = await roomsCollection.where({ room_id: roomId }).get();
    if (roomRes.data.length === 0) {
      await sendResponse(connectionId, 'player_leave', false, null, requestId, '房间不存在');
      return;
    }

    const room = roomRes.data[0];
    
    // 检查用户是否在房间内（未退出成员）
    const playerIndex = room.players.findIndex(
      (player) => player.user_id === userInfo._id && player.has_left !== true
    );
    if (playerIndex === -1) {
      await sendResponse(connectionId, 'player_leave', false, null, requestId, '您不在此房间内');
      return;
    }

    // 构建系统消息数据
    const messageData = {
      room_id: roomId,
      message_type: "system",
      sender_id: userInfo._id,
      sender_name: userInfo.nickname,
      sender_avatar_fileId: userInfo.avatar_fileId,
      timestamp: new Date(),
      detail_data: {
        message_text: `${userInfo.nickname} 退出了房间`,
        action_type: "player_leave"
      }
    };

    // 逻辑标记退出
    const updatedPlayers = room.players.map((player) => {
      if (player.user_id === userInfo._id) {
        return { ...player, has_left: true };
      }
      return player;
    });
    
    // 只统计未退出成员
    const newPlayerCount = updatedPlayers.filter(
      (p) => p.has_left !== true
    ).length;

    // 使用事务处理玩家退出和消息添加
    const transaction = await db.startTransaction();
    
    try {
      // 更新房间玩家信息
      await transaction.collection("rooms").doc(room._id).update({
        players: updatedPlayers,
        current_players: newPlayerCount
      });

      // 添加系统消息记录
      const addResult = await transaction.collection("room_messages").add(messageData);

      // 提交事务
      await transaction.commit();

      // 清空用户的活跃房间ID（调用传统API辅助处理）
      try {
        const userObj = uniCloud.importObject("user");
        
        // 清空用户活跃房间
        const clearResult = await userObj.clearUserActiveRoom(userInfo._id);
        
        if (clearResult.code !== 200) {
          console.warn(`WebSocket退出房间-清空用户活跃房间失败: ${clearResult.message}`);
        }
      } catch (clearError) {
        console.error("WebSocket退出房间-调用clearUserActiveRoom失败:", clearError);
        // 不阻断主流程，只记录错误
      }

      // 准备增量更新数据
      const incrementalData = {
        playerLeft: {
          id: userInfo._id,
          name: userInfo.nickname,
          currentPlayers: newPlayerCount
        },
        // 添加更新后的玩家列表（标记退出玩家）
        updatedPlayers: updatedPlayers.map(player => ({
          id: player.user_id,
          name: player.nickname,
          avatar: player.avatar_fileId,
          score: player.score || 0,
          hasLeft: player.has_left || false
        })),
        newMessage: {
          id: addResult.id,
          type: "system",
          timestamp: formatTimestamp(messageData.timestamp),
          sender: {
            id: userInfo._id,
            name: userInfo.nickname,
            avatar: userInfo.avatar_fileId
          },
          message: messageData.detail_data.message_text,
          actionType: messageData.detail_data.action_type,
          isSelf: true
        }
      };

      // 准备操作发起者的完整响应
      const operatorResponse = {
        action: 'player_leave_result',
        success: true,
        data: { incremental: incrementalData },
        requestId,
        message: '退出房间成功',
        timestamp: Date.now()
      };

      // 准备房间广播数据（给其他玩家）
      const broadcastData = {
        operationType: 'player_leave',
        operator: {
          id: userInfo._id,
          name: userInfo.nickname,
          avatar: userInfo.avatar_fileId
        },
        playerLeft: {
          id: userInfo._id,
          name: userInfo.nickname,
          currentPlayers: newPlayerCount
        },
        incremental: {
          playerLeft: incrementalData.playerLeft,
          updatedPlayers: incrementalData.updatedPlayers,
          newMessage: {
            ...incrementalData.newMessage,
            isSelf: false // 对其他玩家来说不是自己的操作
          }
        }
      };

      // 先广播给其他玩家，再响应操作者（因为操作者即将断开连接）
      const roomConnections = await getRoomConnections(roomId);
      console.log(`房间 ${roomId} 当前连接数: ${roomConnections.size}`);
      
      // 向房间其他玩家广播退出通知
      await broadcastToRoom(roomId, broadcastData, connectionId);
      
      // 向操作发起者发送响应
      const ws = uniCloud.webSocketServer();
      await ws.send(connectionId, JSON.stringify(operatorResponse));
      
      // 检查是否需要自动结束房间（所有成员都已退出）
      if (newPlayerCount === 0) {
        try {
          // 调用传统API的自动结束房间功能
          const roomObj = uniCloud.importObject("room");
          const autoFinishResult = await roomObj.autoFinishRoomIfEmpty(roomId);
          
          if (autoFinishResult.code === 200) {
            console.log(`房间 ${roomId} 已自动结束`);
          } else {
            console.warn(`房间 ${roomId} 自动结束失败: ${autoFinishResult.message}`);
          }
        } catch (autoFinishError) {
          console.error("自动结束房间失败:", autoFinishError);
        }
      }
      
      console.log(`玩家退出处理成功并已广播 - 玩家: ${userInfo.nickname}, 剩余人数: ${newPlayerCount}`);
      
    } catch (transactionError) {
      await transaction.rollback();
      console.error('玩家退出事务失败:', transactionError);
      await sendResponse(connectionId, 'player_leave', false, null, requestId, '退出房间失败，请稍后重试');
    }
    
  } catch (error) {
    console.error('处理玩家退出房间异常:', error);
    await sendResponse(connectionId, 'player_leave', false, null, requestId, '处理玩家退出房间失败');
  }
} 